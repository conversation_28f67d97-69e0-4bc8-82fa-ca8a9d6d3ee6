{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../../../src/utils/auth.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAA+B;AAC/B,wDAA8B;AAC9B,mCAAgC;AAChC,sCAAsD;AACtD,mCAAgD;AAChD,qCAAoD;AAoBpD,qBAAqB;AACR,QAAA,aAAa,GAAG;IAC3B;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,QAAgB;QACzB,IAAI,CAAC;YACH,OAAO,MAAM,kBAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,uBAAc,CAAC,YAAY,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,QAAgB,EAAE,IAAY;QACzC,IAAI,CAAC;YACH,OAAO,MAAM,kBAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,SAAiB,EAAE;QAChC,MAAM,OAAO,GAAG,wEAAwE,CAAC;QACzF,IAAI,QAAQ,GAAG,EAAE,CAAC;QAElB,wDAAwD;QACxD,QAAQ,IAAI,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY;QACtF,QAAQ,IAAI,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY;QACtF,QAAQ,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;QACnE,QAAQ,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;QAE3E,yBAAyB;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,uBAAuB;QACvB,OAAO,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrE,CAAC;CACF,CAAC;AAEF,gBAAgB;AACH,QAAA,QAAQ,GAAG;IACtB;;OAEG;IACH,mBAAmB,CAAC,OAAwC;QAC1D,IAAI,CAAC;YACH,OAAO,sBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAS,CAAC,MAAM,EAAE;gBACzC,SAAS,EAAE,kBAAS,CAAC,SAAS;gBAC9B,MAAM,EAAE,kBAAkB;gBAC1B,QAAQ,EAAE,kBAAkB;aACV,CAAC,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAClF,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,OAAwC;QAC3D,IAAI,CAAC;YACH,OAAO,sBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAS,CAAC,aAAa,EAAE;gBAChD,SAAS,EAAE,kBAAS,CAAC,gBAAgB;gBACrC,MAAM,EAAE,kBAAkB;gBAC1B,QAAQ,EAAE,kBAAkB;aACV,CAAC,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,KAAa;QAC7B,IAAI,CAAC;YACH,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,kBAAS,CAAC,MAAM,EAAE;gBACzC,MAAM,EAAE,kBAAkB;gBAC1B,QAAQ,EAAE,kBAAkB;aAC7B,CAAe,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAC3C,eAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;YACvC,CAAC;iBAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAClD,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC9D,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,KAAa;QAC9B,IAAI,CAAC;YACH,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,kBAAS,CAAC,aAAa,EAAE;gBAChD,MAAM,EAAE,kBAAkB;gBAC1B,QAAQ,EAAE,kBAAkB;aAC7B,CAAe,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAC3C,eAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YACxC,CAAC;iBAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAClD,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC/D,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAa;QAClB,IAAI,CAAC;YACH,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,CAAe,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAC;AAEF,oBAAoB;AACP,QAAA,YAAY,GAAG;IAC1B;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,QAAc;QAC7E,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAA,eAAM,GAAE,CAAC;YAE3B,8BAA8B;YAC9B,MAAM,WAAW,GAAG;gBAClB,MAAM;gBACN,KAAK;gBACL,IAAI;gBACJ,QAAQ;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACvC,CAAC;YAEF,MAAM,sBAAc,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,SAAS;YAE5F,kBAAkB;YAClB,MAAM,YAAY,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YACxD,MAAM,WAAW,GAAG,gBAAQ,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YAC/D,MAAM,YAAY,GAAG,gBAAQ,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAEjE,+CAA+C;YAC/C,MAAM,aAAK,CAAC,GAAG,CAAC,WAAW,SAAS,EAAE,EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,SAAS;YAElF,eAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;YAEtD,OAAO;gBACL,WAAW;gBACX,YAAY;gBACZ,SAAS,EAAE,EAAE,GAAG,EAAE,EAAE,wBAAwB;gBAC5C,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,oBAAoB;aACzD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,YAAoB;QACtC,IAAI,CAAC;YACH,uBAAuB;YACvB,MAAM,OAAO,GAAG,gBAAQ,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,IAAI,CAAC;YACd,CAAC;YAED,0BAA0B;YAC1B,MAAM,WAAW,GAAG,MAAM,sBAAc,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACvE,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;gBACrF,OAAO,IAAI,CAAC;YACd,CAAC;YAED,wCAAwC;YACxC,MAAM,kBAAkB,GAAG,MAAM,aAAK,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;YAC3E,IAAI,kBAAkB,KAAK,YAAY,EAAE,CAAC;gBACxC,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;gBACxE,IAAA,yBAAgB,EAAC,wBAAwB,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACrG,OAAO,IAAI,CAAC;YACd,CAAC;YAED,uBAAuB;YACvB,WAAW,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YACzD,MAAM,sBAAc,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAEvG,sBAAsB;YACtB,MAAM,eAAe,GAAG;gBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B,CAAC;YAEF,MAAM,cAAc,GAAG,gBAAQ,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;YACrE,MAAM,eAAe,GAAG,gBAAQ,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAEvE,gCAAgC;YAChC,MAAM,aAAK,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,SAAS,EAAE,EAAE,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAEnF,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;YAE1F,OAAO;gBACL,WAAW,EAAE,cAAc;gBAC3B,YAAY,EAAE,eAAe;gBAC7B,SAAS,EAAE,EAAE,GAAG,EAAE,EAAE,wBAAwB;gBAC5C,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,oBAAoB;aACzD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,SAAiB;QACrC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,sBAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC/D,OAAO,WAAW,KAAK,IAAI,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QACvC,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,sBAAc,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAE9C,uBAAuB;YACvB,MAAM,aAAK,CAAC,GAAG,CAAC,WAAW,SAAS,EAAE,CAAC,CAAC;YAExC,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAAC,MAAc;QAC5C,IAAI,CAAC;YACH,yEAAyE;YACzE,6CAA6C;YAC7C,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,SAAiB;QAC3C,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,sBAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC/D,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,WAAW,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YACzD,MAAM,sBAAc,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAEnG,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YACrE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAC;AAEF,oBAAoB;AACP,QAAA,SAAS,GAAG;IACvB;;OAEG;IACH,sBAAsB,CAAC,UAA8B;QACnD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,SAAiB,CAAC;QACzC,MAAM,MAAM,GAAG,YAAY,CAAC;QAC5B,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5D,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAA,eAAM,EAAC,EAAE,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,QAAgB;QACvC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,mCAAmC"}