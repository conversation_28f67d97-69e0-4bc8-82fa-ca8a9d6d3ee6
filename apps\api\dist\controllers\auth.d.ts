import { Request, Response } from 'express';
/**
 * User registration
 */
export declare const register: (req: Request, res: Response, next: import("express").NextFunction) => void;
/**
 * User login
 */
export declare const login: (req: Request, res: Response, next: import("express").NextFunction) => void;
/**
 * Refresh access token
 */
export declare const refreshToken: (req: Request, res: Response, next: import("express").NextFunction) => void;
/**
 * User logout
 */
export declare const logout: (req: Request, res: Response, next: import("express").NextFunction) => void;
/**
 * Logout from all devices
 */
export declare const logoutAll: (req: Request, res: Response, next: import("express").NextFunction) => void;
/**
 * Verify email
 */
export declare const verifyEmail: (req: Request, res: Response, next: import("express").NextFunction) => void;
/**
 * Resend email verification
 */
export declare const resendEmailVerification: (req: Request, res: Response, next: import("express").NextFunction) => void;
/**
 * Request password reset
 */
export declare const requestPasswordReset: (req: Request, res: Response, next: import("express").NextFunction) => void;
/**
 * Reset password
 */
export declare const resetPassword: (req: Request, res: Response, next: import("express").NextFunction) => void;
/**
 * Get current user profile
 */
export declare const getProfile: (req: Request, res: Response, next: import("express").NextFunction) => void;
declare const _default: {
    register: (req: Request, res: Response, next: import("express").NextFunction) => void;
    login: (req: Request, res: Response, next: import("express").NextFunction) => void;
    refreshToken: (req: Request, res: Response, next: import("express").NextFunction) => void;
    logout: (req: Request, res: Response, next: import("express").NextFunction) => void;
    logoutAll: (req: Request, res: Response, next: import("express").NextFunction) => void;
    verifyEmail: (req: Request, res: Response, next: import("express").NextFunction) => void;
    resendEmailVerification: (req: Request, res: Response, next: import("express").NextFunction) => void;
    requestPasswordReset: (req: Request, res: Response, next: import("express").NextFunction) => void;
    resetPassword: (req: Request, res: Response, next: import("express").NextFunction) => void;
    getProfile: (req: Request, res: Response, next: import("express").NextFunction) => void;
};
export default _default;
//# sourceMappingURL=auth.d.ts.map