{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../src/middleware/error.ts"], "names": [], "mappings": ";;;AAEA,6BAA+B;AAC/B,4CAAyC;AACzC,sCAA0C;AAE1C,qBAAqB;AACrB,MAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,CAAS;IACnB,IAAI,CAAS;IACb,aAAa,CAAU;IACvB,OAAO,CAAO;IAErB,YACE,OAAe,EACf,aAAqB,GAAG,EACxB,OAAe,gBAAgB,EAC/B,gBAAyB,IAAI,EAC7B,OAAa;QAEb,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AArBD,4BAqBC;AAcD;;GAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,KAAU,EAAY,EAAE;IACjD,gEAAgE;IAChE,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,8BAA8B;gBAC9B,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,MAA8B,CAAC;gBACzD,MAAM,SAAS,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC;gBACxC,OAAO,IAAI,QAAQ,CACjB,GAAG,SAAS,iBAAiB,EAC7B,GAAG,EACH,iBAAiB,EACjB,IAAI,EACJ,EAAE,KAAK,EAAE,SAAS,EAAE,CACrB,CAAC;YAEJ,KAAK,OAAO;gBACV,mBAAmB;gBACnB,OAAO,IAAI,QAAQ,CACjB,kBAAkB,EAClB,GAAG,EACH,WAAW,CACZ,CAAC;YAEJ,KAAK,OAAO;gBACV,mCAAmC;gBACnC,OAAO,IAAI,QAAQ,CACjB,0BAA0B,EAC1B,GAAG,EACH,uBAAuB,CACxB,CAAC;YAEJ,KAAK,OAAO;gBACV,8BAA8B;gBAC9B,OAAO,IAAI,QAAQ,CACjB,2BAA2B,EAC3B,GAAG,EACH,2BAA2B,CAC5B,CAAC;YAEJ,KAAK,OAAO;gBACV,uBAAuB;gBACvB,OAAO,IAAI,QAAQ,CACjB,0BAA0B,EAC1B,GAAG,EACH,iBAAiB,CAClB,CAAC;YAEJ,KAAK,OAAO;gBACV,wBAAwB;gBACxB,OAAO,IAAI,QAAQ,CACjB,2BAA2B,EAC3B,GAAG,EACH,kBAAkB,CACnB,CAAC;YAEJ;gBACE,OAAO,IAAI,QAAQ,CACjB,2BAA2B,EAC3B,GAAG,EACH,gBAAgB,EAChB,IAAI,EACJ,EAAE,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,CAC3B,CAAC;QACN,CAAC;IACH,CAAC;IAED,OAAO,IAAI,QAAQ,CACjB,2BAA2B,EAC3B,GAAG,EACH,gBAAgB,CACjB,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG,CAAC,KAAe,EAAY,EAAE;IACnD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACxC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACzB,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,IAAI,EAAE,GAAG,CAAC,IAAI;KACf,CAAC,CAAC,CAAC;IAEJ,OAAO,IAAI,QAAQ,CACjB,mBAAmB,EACnB,GAAG,EACH,kBAAkB,EAClB,IAAI,EACJ,EAAE,MAAM,EAAE,CACX,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG,CAAC,KAAY,EAAY,EAAE;IAChD,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACvC,OAAO,IAAI,QAAQ,CACjB,mBAAmB,EACnB,GAAG,EACH,eAAe,CAChB,CAAC;IACJ,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACvC,OAAO,IAAI,QAAQ,CACjB,eAAe,EACf,GAAG,EACH,eAAe,CAChB,CAAC;IACJ,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;QACpC,OAAO,IAAI,QAAQ,CACjB,sBAAsB,EACtB,GAAG,EACH,kBAAkB,CACnB,CAAC;IACJ,CAAC;IAED,OAAO,IAAI,QAAQ,CACjB,uBAAuB,EACvB,GAAG,EACH,YAAY,CACb,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,KAAU,EAAY,EAAE;IACjD,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACrC,OAAO,IAAI,QAAQ,CACjB,qBAAqB,EACrB,GAAG,EACH,gBAAgB,EAChB,IAAI,EACJ,EAAE,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,CACzB,CAAC;IACJ,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;QACtC,OAAO,IAAI,QAAQ,CACjB,gBAAgB,EAChB,GAAG,EACH,gBAAgB,EAChB,IAAI,EACJ,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,CAC1B,CAAC;IACJ,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,uBAAuB,EAAE,CAAC;QAC3C,OAAO,IAAI,QAAQ,CACjB,uBAAuB,EACvB,GAAG,EACH,uBAAuB,EACvB,IAAI,EACJ,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,CAC3B,CAAC;IACJ,CAAC;IAED,OAAO,IAAI,QAAQ,CACjB,oBAAoB,EACpB,GAAG,EACH,mBAAmB,CACpB,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,KAAe,EAAE,GAAY,EAAE,GAAa,EAAQ,EAAE;IAC/E,MAAM,QAAQ,GAAkB;QAC9B,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,MAAM,EAAE,GAAG,CAAC,MAAM;KACnB,CAAC;IAEF,uDAAuD;IACvD,IAAI,sBAAa,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;QACzC,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QACnC,CAAC;IACH,CAAC;IAED,iCAAiC;IACjC,IAAI,sBAAa,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QACjC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC/B,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,YAAY,GAAG,CAC1B,KAAY,EACZ,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,QAAkB,CAAC;IAEvB,2BAA2B;IAC3B,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,QAAQ,GAAG,KAAK,CAAC;IACnB,CAAC;SAAM,IAAK,KAAa,CAAC,IAAI,IAAK,KAAa,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACtE,4BAA4B;QAC5B,QAAQ,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;SAAM,IAAI,KAAK,YAAY,cAAQ,EAAE,CAAC;QACrC,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QACxE,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;QACxC,QAAQ,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;QAC3D,QAAQ,GAAG,IAAI,QAAQ,CACrB,8BAA8B,EAC9B,GAAG,EACH,cAAc,CACf,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,gBAAgB;QAChB,QAAQ,GAAG,IAAI,QAAQ,CACrB,sBAAa,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAuB,EACvD,GAAG,EACH,gBAAgB,EAChB,KAAK,CACN,CAAC;IACJ,CAAC;IAED,YAAY;IACZ,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;QAC1D,eAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE;YAChC,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;YACpB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACjC,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,eAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC/B,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;SACrB,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxC,CAAC,CAAC;AA7DW,QAAA,YAAY,gBA6DvB;AAEF;;GAEG;AACI,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACvF,MAAM,KAAK,GAAG,IAAI,QAAQ,CACxB,SAAS,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,YAAY,EAC3C,GAAG,EACH,iBAAiB,CAClB,CAAC;IAEF,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC,CAAC;AARW,QAAA,eAAe,mBAQ1B;AAEF;;GAEG;AACI,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAEF;;GAEG;AACU,QAAA,WAAW,GAAG;IACzB,UAAU,EAAE,CAAC,OAAe,EAAE,OAAe,aAAa,EAAE,OAAa,EAAE,EAAE,CAC3E,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;IAEjD,YAAY,EAAE,CAAC,UAAkB,cAAc,EAAE,OAAe,cAAc,EAAE,EAAE,CAChF,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC;IAElC,SAAS,EAAE,CAAC,UAAkB,WAAW,EAAE,OAAe,WAAW,EAAE,EAAE,CACvE,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC;IAElC,QAAQ,EAAE,CAAC,UAAkB,WAAW,EAAE,OAAe,WAAW,EAAE,EAAE,CACtE,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC;IAElC,QAAQ,EAAE,CAAC,OAAe,EAAE,OAAe,UAAU,EAAE,OAAa,EAAE,EAAE,CACtE,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;IAEjD,mBAAmB,EAAE,CAAC,OAAe,EAAE,OAAe,sBAAsB,EAAE,OAAa,EAAE,EAAE,CAC7F,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;IAEjD,eAAe,EAAE,CAAC,UAAkB,mBAAmB,EAAE,OAAe,mBAAmB,EAAE,EAAE,CAC7F,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC;IAElC,QAAQ,EAAE,CAAC,UAAkB,uBAAuB,EAAE,OAAe,gBAAgB,EAAE,EAAE,CACvF,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;IAEzC,kBAAkB,EAAE,CAAC,UAAkB,qBAAqB,EAAE,OAAe,qBAAqB,EAAE,EAAE,CACpG,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC;CACnC,CAAC;AAEF,kBAAe,oBAAY,CAAC"}