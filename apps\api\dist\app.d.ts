import express from 'express';
declare class App {
    app: express.Application;
    constructor();
    private initializeMiddleware;
    private initializeRoutes;
    private initializeSwagger;
    private initializeErrorHandling;
    private healthCheck;
    private checkDatabaseHealth;
    private checkRedisHealth;
    initialize(): Promise<void>;
    shutdown(): Promise<void>;
}
export default App;
//# sourceMappingURL=app.d.ts.map