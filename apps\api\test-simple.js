console.log('Starting simple test...');

try {
  console.log('Loading config...');
  const config = require('./dist/config/index.js');
  console.log('✅ Config loaded:', config.config.PORT);
  
  console.log('Loading logger...');
  const { logger } = require('./dist/utils/logger.js');
  console.log('✅ Logger loaded');
  
  console.log('Loading App class...');
  const App = require('./dist/app.js').default;
  console.log('✅ App class loaded');
  
  console.log('Creating app instance...');
  const app = new App();
  console.log('✅ App instance created');
  
  console.log('Test completed successfully!');
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error('Stack:', error.stack);
}
