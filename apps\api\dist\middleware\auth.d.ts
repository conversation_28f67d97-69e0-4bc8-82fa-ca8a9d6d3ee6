import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                role: string;
                sessionId: string;
                firstName: string;
                lastName: string;
                status: string;
                emailVerified: boolean;
                phoneVerified: boolean;
            };
        }
    }
}
/**
 * Authentication middleware - verifies JW<PERSON> token and loads user data
 */
export declare const authenticate: (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export declare const optionalAuthenticate: (req: Request, res: Response, next: NextFunction) => Promise<void>;
/**
 * Role-based authorization middleware
 */
export declare const authorize: (...allowedRoles: string[]) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Email verification requirement middleware
 */
export declare const requireEmailVerification: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Phone verification requirement middleware
 */
export declare const requirePhoneVerification: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Resource ownership middleware - checks if user owns the resource
 */
export declare const requireOwnership: (resourceIdParam?: string, userIdField?: string) => (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Admin only middleware
 */
export declare const adminOnly: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Expert only middleware
 */
export declare const expertOnly: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Client only middleware
 */
export declare const clientOnly: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Expert or Admin middleware
 */
export declare const expertOrAdmin: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Client or Admin middleware
 */
export declare const clientOrAdmin: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Any authenticated user middleware
 */
export declare const anyUser: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
//# sourceMappingURL=auth.d.ts.map