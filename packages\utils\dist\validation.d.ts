export declare const userRegistrationSchema: any;
export declare const userLoginSchema: any;
export declare const userProfileSchema: any;
export declare const serviceSchema: any;
export declare const bookingSchema: any;
export declare const messageSchema: any;
export declare const reviewSchema: any;
export declare const fileUploadSchema: any;
export declare const validateEmail: (email: string) => boolean;
export declare const validatePassword: (password: string) => boolean;
export declare const validatePhone: (phone: string) => boolean;
export declare const validateUrl: (url: string) => boolean;
export declare const validateSlug: (slug: string) => boolean;
export declare const validateArabicText: (text: string) => boolean;
export declare const validateEnglishText: (text: string) => boolean;
export declare const validateSyrianPhone: (phone: string) => boolean;
export declare const validateCreditCard: (cardNumber: string) => boolean;
export declare const validateCVV: (cvv: string, cardType?: string) => boolean;
export declare const validateExpiryDate: (month: string, year: string) => boolean;
export declare const validateFileType: (filename: string, allowedTypes: string[]) => boolean;
export declare const validateImageFile: (filename: string) => boolean;
export declare const validateDocumentFile: (filename: string) => boolean;
export declare const validateVideoFile: (filename: string) => boolean;
export declare const validateAudioFile: (filename: string) => boolean;
//# sourceMappingURL=validation.d.ts.map