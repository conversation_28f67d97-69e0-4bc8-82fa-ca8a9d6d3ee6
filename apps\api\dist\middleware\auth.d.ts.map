{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAM1D,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,OAAO,CAAC;QAChB,UAAU,OAAO;YACf,IAAI,CAAC,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC;gBACX,KAAK,EAAE,MAAM,CAAC;gBACd,IAAI,EAAE,MAAM,CAAC;gBACb,SAAS,EAAE,MAAM,CAAC;gBAClB,SAAS,EAAE,MAAM,CAAC;gBAClB,QAAQ,EAAE,MAAM,CAAC;gBACjB,MAAM,EAAE,MAAM,CAAC;gBACf,aAAa,EAAE,OAAO,CAAC;gBACvB,aAAa,EAAE,OAAO,CAAC;aACxB,CAAC;SACH;KACF;CACF;AAED;;GAEG;AACH,eAAO,MAAM,YAAY,GAAU,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,4DAuFjF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,oBAAoB,GAAU,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,kBA2CzF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,SAAS,GAAI,GAAG,cAAc,MAAM,EAAE,MACzC,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,mDA0BxD,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,wBAAwB,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,mDAkBvF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,wBAAwB,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,mDAkBvF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,gBAAgB,GAAI,kBAAiB,MAAa,EAAE,cAAa,MAAiB,MAC/E,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,4DA6C9D,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,SAAS,QAhIP,OAAO,OAAO,QAAQ,QAAQ,YAAY,mDAgId,CAAC;AAE5C;;GAEG;AACH,eAAO,MAAM,UAAU,QArIR,OAAO,OAAO,QAAQ,QAAQ,YAAY,mDAqIZ,CAAC;AAE9C;;GAEG;AACH,eAAO,MAAM,UAAU,QA1IR,OAAO,OAAO,QAAQ,QAAQ,YAAY,mDA0IZ,CAAC;AAE9C;;GAEG;AACH,eAAO,MAAM,aAAa,QA/IX,OAAO,OAAO,QAAQ,QAAQ,YAAY,mDA+IA,CAAC;AAE1D;;GAEG;AACH,eAAO,MAAM,aAAa,QApJX,OAAO,OAAO,QAAQ,QAAQ,YAAY,mDAoJA,CAAC;AAE1D;;GAEG;AACH,eAAO,MAAM,OAAO,QAzJL,OAAO,OAAO,QAAQ,QAAQ,YAAY,mDAyJI,CAAC"}