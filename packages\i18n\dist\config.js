"use strict";
const i18n = require('i18next');
const { initReactI18next } = require('react-i18next');
// Import translation files
const arCommon = require('../locales/ar/common.json');
const arAuth = require('../locales/ar/auth.json');
const arExpert = require('../locales/ar/expert.json');
const arClient = require('../locales/ar/client.json');
const enCommon = require('../locales/en/common.json');
// Translation resources
const resources = {
    ar: {
        common: arCommon,
        auth: arAuth,
        expert: arExpert,
        client: arClient,
    },
    en: {
        common: enCommon,
        auth: {},
        expert: {},
        client: {},
    },
};
// i18n configuration
const i18nConfig = {
    resources,
    lng: 'ar', // Default language
    fallbackLng: 'ar', // Fallback language
    defaultNS: 'common',
    ns: ['common', 'auth', 'expert', 'client', 'booking', 'payment', 'chat', 'admin'],
    interpolation: {
        escapeValue: false, // React already escapes values
        formatSeparator: ',',
        format: (value, format, lng) => {
            if (format === 'uppercase')
                return value.toUpperCase();
            if (format === 'lowercase')
                return value.toLowerCase();
            if (format === 'currency') {
                return new Intl.NumberFormat(lng === 'ar' ? 'ar-SY' : 'en-US', {
                    style: 'currency',
                    currency: 'USD',
                }).format(value);
            }
            if (format === 'date') {
                return new Intl.DateTimeFormat(lng === 'ar' ? 'ar-SY' : 'en-US').format(new Date(value));
            }
            return value;
        },
    },
    detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        lookupLocalStorage: 'freela-language',
        caches: ['localStorage'],
    },
    react: {
        useSuspense: false,
        bindI18n: 'languageChanged',
        bindI18nStore: '',
        transEmptyNodeValue: '',
        transSupportBasicHtmlNodes: true,
        transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em'],
    },
    debug: process.env.NODE_ENV === 'development',
};
// Initialize i18n
i18n.use(initReactI18next).init(i18nConfig);
// Language detection for different environments
const detectLanguage = () => {
    // Check localStorage first
    if (typeof window !== 'undefined') {
        const stored = localStorage.getItem('freela-language');
        if (stored && ['ar', 'en'].includes(stored)) {
            return stored;
        }
    }
    // Check navigator language
    if (typeof navigator !== 'undefined') {
        const browserLang = navigator.language || navigator.userLanguage;
        if (browserLang.startsWith('ar')) {
            return 'ar';
        }
    }
    // Default to Arabic
    return 'ar';
};
// Set language and persist
const setLanguage = (language) => {
    i18n.changeLanguage(language);
    if (typeof window !== 'undefined') {
        localStorage.setItem('freela-language', language);
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    }
};
// Get current language
const getCurrentLanguage = () => {
    return i18n.language || 'ar';
};
// Check if current language is RTL
const isRTL = (language) => {
    const lang = language || getCurrentLanguage();
    return lang === 'ar';
};
// Get text direction
const getDirection = (language) => {
    return isRTL(language) ? 'rtl' : 'ltr';
};
// Get text alignment
const getTextAlign = (language) => {
    return isRTL(language) ? 'right' : 'left';
};
// Initialize language on app start
const initializeLanguage = () => {
    const detectedLanguage = detectLanguage();
    setLanguage(detectedLanguage);
};
// Export all functions and config for CommonJS compatibility
module.exports = {
    i18nConfig,
    detectLanguage,
    setLanguage,
    getCurrentLanguage,
    isRTL,
    getDirection,
    getTextAlign,
    initializeLanguage,
    default: i18n,
};
//# sourceMappingURL=config.js.map