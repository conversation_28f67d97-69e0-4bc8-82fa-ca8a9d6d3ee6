"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const app_1 = __importDefault(require("./app"));
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception', {
        message: error.message,
        stack: error.stack,
    });
    process.exit(1);
});
// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection', {
        reason: reason?.message || reason,
        stack: reason?.stack,
        promise: promise.toString(),
    });
    process.exit(1);
});
// Handle SIGTERM signal
process.on('SIGTERM', async () => {
    logger_1.logger.info('SIGTERM received, shutting down gracefully');
    await gracefulShutdown();
});
// Handle SIGINT signal (Ctrl+C)
process.on('SIGINT', async () => {
    logger_1.logger.info('SIGINT received, shutting down gracefully');
    await gracefulShutdown();
});
let server;
let app;
async function startServer() {
    try {
        // Create and initialize app
        app = new app_1.default();
        await app.initialize();
        // Start HTTP server
        server = app.app.listen(config_1.config.PORT, () => {
            logger_1.logger.info(`🚀 Server running on port ${config_1.config.PORT}`);
            logger_1.logger.info(`📚 API Documentation: http://localhost:${config_1.config.PORT}/api/${config_1.config.API_VERSION}/docs`);
            logger_1.logger.info(`🏥 Health Check: http://localhost:${config_1.config.PORT}/health`);
            logger_1.logger.info(`🌍 Environment: ${config_1.config.NODE_ENV}`);
        });
        // Handle server errors
        server.on('error', (error) => {
            if (error.syscall !== 'listen') {
                throw error;
            }
            const bind = typeof config_1.config.PORT === 'string'
                ? 'Pipe ' + config_1.config.PORT
                : 'Port ' + config_1.config.PORT;
            switch (error.code) {
                case 'EACCES':
                    logger_1.logger.error(`${bind} requires elevated privileges`);
                    process.exit(1);
                    break;
                case 'EADDRINUSE':
                    logger_1.logger.error(`${bind} is already in use`);
                    process.exit(1);
                    break;
                default:
                    throw error;
            }
        });
        // Set server timeout
        server.timeout = 30000; // 30 seconds
    }
    catch (error) {
        logger_1.logger.error('Failed to start server', { error });
        process.exit(1);
    }
}
async function gracefulShutdown() {
    try {
        logger_1.logger.info('Starting graceful shutdown...');
        // Stop accepting new connections
        if (server) {
            server.close((error) => {
                if (error) {
                    logger_1.logger.error('Error closing server', { error });
                }
                else {
                    logger_1.logger.info('✅ HTTP server closed');
                }
            });
        }
        // Shutdown application
        if (app) {
            await app.shutdown();
        }
        logger_1.logger.info('✅ Graceful shutdown completed');
        process.exit(0);
    }
    catch (error) {
        logger_1.logger.error('❌ Error during graceful shutdown', { error });
        process.exit(1);
    }
}
// Start the server
startServer().catch((error) => {
    logger_1.logger.error('Failed to start application', { error });
    process.exit(1);
});
exports.default = app || null;
//# sourceMappingURL=index.js.map