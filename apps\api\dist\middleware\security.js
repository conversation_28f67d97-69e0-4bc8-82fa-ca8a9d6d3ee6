"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.suspiciousActivityDetection = exports.requestId = exports.securityHeaders = exports.requestSizeLimit = exports.createUserRateLimit = exports.createIPRateLimit = exports.authRateLimitMiddleware = exports.rateLimitMiddleware = exports.compressionMiddleware = exports.helmetMiddleware = exports.corsMiddleware = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const helmet_1 = __importDefault(require("helmet"));
const cors_1 = __importDefault(require("cors"));
const compression_1 = __importDefault(require("compression"));
const config_1 = require("../config");
const redis_1 = require("../utils/redis");
const logger_1 = require("../utils/logger");
/**
 * CORS configuration
 */
exports.corsMiddleware = (0, cors_1.default)({
    origin: (origin, callback) => {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin)
            return callback(null, true);
        // Check if origin is in allowed list
        if (config_1.corsOrigins.includes(origin) || config_1.corsOrigins.includes('*')) {
            return callback(null, true);
        }
        // In development, allow localhost with any port
        if (config_1.isDevelopment && origin.includes('localhost')) {
            return callback(null, true);
        }
        (0, logger_1.logSecurityEvent)('cors_violation', { origin }, { ip: 'unknown' });
        callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-API-Key',
        'X-Client-Version',
        'X-Request-ID',
    ],
    exposedHeaders: [
        'X-Total-Count',
        'X-Page-Count',
        'X-Current-Page',
        'X-Per-Page',
        'X-Rate-Limit-Remaining',
        'X-Rate-Limit-Reset',
    ],
    maxAge: 86400, // 24 hours
});
/**
 * Helmet security headers
 */
exports.helmetMiddleware = (0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
    },
});
/**
 * Compression middleware
 */
exports.compressionMiddleware = (0, compression_1.default)({
    filter: (req, res) => {
        if (req.headers['x-no-compression']) {
            return false;
        }
        return compression_1.default.filter(req, res);
    },
    level: 6,
    threshold: 1024,
});
/**
 * Redis-based rate limiter
 */
class RedisRateLimiter {
    windowMs;
    maxRequests;
    constructor(windowMs, maxRequests) {
        this.windowMs = windowMs;
        this.maxRequests = maxRequests;
    }
    async isAllowed(key) {
        try {
            const now = Date.now();
            const window = Math.floor(now / this.windowMs);
            const redisKey = `rate_limit:${key}:${window}`;
            // Get current count
            const current = await redis_1.redis.get(redisKey);
            const count = current ? parseInt(current) : 0;
            if (count >= this.maxRequests) {
                const resetTime = (window + 1) * this.windowMs;
                return {
                    allowed: false,
                    remaining: 0,
                    resetTime,
                };
            }
            // Increment counter
            const newCount = count + 1;
            await redis_1.redis.set(redisKey, newCount.toString(), Math.ceil(this.windowMs / 1000));
            const resetTime = (window + 1) * this.windowMs;
            return {
                allowed: true,
                remaining: this.maxRequests - newCount,
                resetTime,
            };
        }
        catch (error) {
            logger_1.logger.error('Redis rate limiter error', { error, key });
            // Fallback to allow request if Redis is down
            return {
                allowed: true,
                remaining: this.maxRequests,
                resetTime: Date.now() + this.windowMs,
            };
        }
    }
}
/**
 * General rate limiting middleware
 */
exports.rateLimitMiddleware = (0, express_rate_limit_1.default)({
    windowMs: config_1.rateLimitConfig.windowMs,
    max: config_1.rateLimitConfig.maxRequests,
    message: {
        success: false,
        message: 'Too many requests, please try again later',
        code: 'RATE_LIMIT_EXCEEDED',
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        (0, logger_1.logSecurityEvent)('rate_limit_exceeded', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            endpoint: req.path,
        }, req);
        res.status(429).json({
            success: false,
            message: 'Too many requests, please try again later',
            code: 'RATE_LIMIT_EXCEEDED',
        });
    },
});
/**
 * Strict rate limiting for authentication endpoints
 */
exports.authRateLimitMiddleware = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    skipSuccessfulRequests: true,
    message: {
        success: false,
        message: 'Too many authentication attempts, please try again later',
        code: 'AUTH_RATE_LIMIT_EXCEEDED',
    },
    handler: (req, res) => {
        (0, logger_1.logSecurityEvent)('auth_rate_limit_exceeded', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            endpoint: req.path,
            email: req.body?.email,
        }, req);
        res.status(429).json({
            success: false,
            message: 'Too many authentication attempts, please try again later',
            code: 'AUTH_RATE_LIMIT_EXCEEDED',
        });
    },
});
/**
 * IP-based rate limiting
 */
const createIPRateLimit = (windowMs, maxRequests) => {
    const limiter = new RedisRateLimiter(windowMs, maxRequests);
    return async (req, res, next) => {
        try {
            const ip = req.ip || req.connection.remoteAddress || 'unknown';
            const result = await limiter.isAllowed(ip);
            // Set rate limit headers
            res.set({
                'X-RateLimit-Limit': maxRequests.toString(),
                'X-RateLimit-Remaining': result.remaining.toString(),
                'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
            });
            if (!result.allowed) {
                (0, logger_1.logSecurityEvent)('ip_rate_limit_exceeded', { ip }, req);
                return res.status(429).json({
                    success: false,
                    message: 'Too many requests from this IP',
                    code: 'IP_RATE_LIMIT_EXCEEDED',
                });
            }
            next();
        }
        catch (error) {
            logger_1.logger.error('IP rate limit middleware error', { error });
            next(); // Continue on error
        }
    };
};
exports.createIPRateLimit = createIPRateLimit;
/**
 * User-based rate limiting
 */
const createUserRateLimit = (windowMs, maxRequests) => {
    const limiter = new RedisRateLimiter(windowMs, maxRequests);
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return next();
            }
            const userId = req.user.id;
            const result = await limiter.isAllowed(userId);
            // Set rate limit headers
            res.set({
                'X-RateLimit-Limit': maxRequests.toString(),
                'X-RateLimit-Remaining': result.remaining.toString(),
                'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
            });
            if (!result.allowed) {
                (0, logger_1.logSecurityEvent)('user_rate_limit_exceeded', { userId }, req);
                return res.status(429).json({
                    success: false,
                    message: 'Too many requests from this user',
                    code: 'USER_RATE_LIMIT_EXCEEDED',
                });
            }
            next();
        }
        catch (error) {
            logger_1.logger.error('User rate limit middleware error', { error });
            next(); // Continue on error
        }
    };
};
exports.createUserRateLimit = createUserRateLimit;
/**
 * Request size limiting middleware
 */
const requestSizeLimit = (maxSize = '10mb') => {
    return (req, res, next) => {
        const contentLength = req.get('content-length');
        if (contentLength) {
            const size = parseInt(contentLength);
            const maxSizeBytes = parseSize(maxSize);
            if (size > maxSizeBytes) {
                (0, logger_1.logSecurityEvent)('request_size_exceeded', {
                    size,
                    maxSize: maxSizeBytes,
                    endpoint: req.path,
                }, req);
                return res.status(413).json({
                    success: false,
                    message: 'Request entity too large',
                    code: 'REQUEST_TOO_LARGE',
                });
            }
        }
        next();
    };
};
exports.requestSizeLimit = requestSizeLimit;
/**
 * Parse size string to bytes
 */
const parseSize = (size) => {
    const units = {
        b: 1,
        kb: 1024,
        mb: 1024 * 1024,
        gb: 1024 * 1024 * 1024,
    };
    const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
    if (!match) {
        throw new Error('Invalid size format');
    }
    const value = parseFloat(match[1]);
    const unit = match[2] || 'b';
    return Math.floor(value * units[unit]);
};
/**
 * Security headers middleware
 */
const securityHeaders = (req, res, next) => {
    // Remove sensitive headers
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');
    // Add security headers
    res.set({
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
    });
    next();
};
exports.securityHeaders = securityHeaders;
/**
 * Request ID middleware
 */
const requestId = (req, res, next) => {
    const requestId = req.get('X-Request-ID') || generateRequestId();
    req.headers['x-request-id'] = requestId;
    res.set('X-Request-ID', requestId);
    next();
};
exports.requestId = requestId;
/**
 * Generate unique request ID
 */
const generateRequestId = () => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
/**
 * Suspicious activity detection
 */
const suspiciousActivityDetection = (req, res, next) => {
    const userAgent = req.get('User-Agent') || '';
    const ip = req.ip;
    // Check for suspicious patterns
    const suspiciousPatterns = [
        /bot/i,
        /crawler/i,
        /spider/i,
        /scraper/i,
        /hack/i,
        /exploit/i,
    ];
    const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));
    if (isSuspicious) {
        (0, logger_1.logSecurityEvent)('suspicious_user_agent', {
            userAgent,
            ip,
            endpoint: req.path,
        }, req);
    }
    // Check for SQL injection patterns in query parameters
    const queryString = JSON.stringify(req.query);
    const sqlInjectionPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
        /(\'|\"|;|--|\*|\/\*|\*\/)/,
    ];
    const hasSQLInjection = sqlInjectionPatterns.some(pattern => pattern.test(queryString));
    if (hasSQLInjection) {
        (0, logger_1.logSecurityEvent)('sql_injection_attempt', {
            query: req.query,
            ip,
            endpoint: req.path,
        }, req);
        return res.status(400).json({
            success: false,
            message: 'Invalid request parameters',
            code: 'INVALID_PARAMETERS',
        });
    }
    next();
};
exports.suspiciousActivityDetection = suspiciousActivityDetection;
exports.default = {
    cors: exports.corsMiddleware,
    helmet: exports.helmetMiddleware,
    compression: exports.compressionMiddleware,
    rateLimit: exports.rateLimitMiddleware,
    authRateLimit: exports.authRateLimitMiddleware,
    requestSizeLimit: exports.requestSizeLimit,
    securityHeaders: exports.securityHeaders,
    requestId: exports.requestId,
    suspiciousActivityDetection: exports.suspiciousActivityDetection,
    createIPRateLimit: exports.createIPRateLimit,
    createUserRateLimit: exports.createUserRateLimit,
};
//# sourceMappingURL=security.js.map