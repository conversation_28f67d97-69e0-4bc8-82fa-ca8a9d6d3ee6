import { z } from 'zod';
export declare const emailSchema: z.ZodString;
export declare const passwordSchema: z.ZodString;
export declare const phoneSchema: z.ZodString;
export declare const urlSchema: z.ZodString;
export declare const slugSchema: z.ZodString;
export declare const userRegistrationSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    firstName: z.ZodString;
    lastName: z.ZodString;
    phone: z.ZodOptional<z.ZodString>;
    role: z.ZodEffects<z.ZodEnum<["CLIENT", "EXPERT", "client", "expert"]>, string, "EXPERT" | "CLIENT" | "client" | "expert">;
    language: z.ZodDefault<z.ZodEnum<["ar", "en"]>>;
    acceptTerms: z.ZodEffects<z.ZodBoolean, boolean, boolean>;
}, "strip", z.ZodTypeAny, {
    role: string;
    password: string;
    email: string;
    firstName: string;
    lastName: string;
    language: "ar" | "en";
    acceptTerms: boolean;
    phone?: string | undefined;
}, {
    role: "EXPERT" | "CLIENT" | "client" | "expert";
    password: string;
    email: string;
    firstName: string;
    lastName: string;
    acceptTerms: boolean;
    phone?: string | undefined;
    language?: "ar" | "en" | undefined;
}>;
export declare const userLoginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    rememberMe: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    password: string;
    email: string;
    rememberMe?: boolean | undefined;
}, {
    password: string;
    email: string;
    rememberMe?: boolean | undefined;
}>;
export declare const userProfileSchema: z.ZodObject<{
    firstName: z.ZodString;
    lastName: z.ZodString;
    phone: z.ZodOptional<z.ZodString>;
    bio: z.ZodOptional<z.ZodString>;
    website: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodObject<{
        governorate: z.ZodString;
        city: z.ZodString;
        district: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        governorate: string;
        city: string;
        district?: string | undefined;
    }, {
        governorate: string;
        city: string;
        district?: string | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    firstName: string;
    lastName: string;
    location?: {
        governorate: string;
        city: string;
        district?: string | undefined;
    } | undefined;
    phone?: string | undefined;
    bio?: string | undefined;
    website?: string | undefined;
}, {
    firstName: string;
    lastName: string;
    location?: {
        governorate: string;
        city: string;
        district?: string | undefined;
    } | undefined;
    phone?: string | undefined;
    bio?: string | undefined;
    website?: string | undefined;
}>;
export declare const serviceSchema: z.ZodObject<{
    title: z.ZodObject<{
        ar: z.ZodString;
        en: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        ar: string;
        en?: string | undefined;
    }, {
        ar: string;
        en?: string | undefined;
    }>;
    description: z.ZodObject<{
        ar: z.ZodString;
        en: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        ar: string;
        en?: string | undefined;
    }, {
        ar: string;
        en?: string | undefined;
    }>;
    categoryId: z.ZodString;
    tags: z.ZodArray<z.ZodString, "many">;
    pricing: z.ZodObject<{
        type: z.ZodEnum<["fixed", "hourly", "package", "custom"]>;
        basePrice: z.ZodNumber;
        currency: z.ZodDefault<z.ZodEnum<["USD", "SYP"]>>;
    }, "strip", z.ZodTypeAny, {
        type: "fixed" | "custom" | "hourly" | "package";
        currency: "USD" | "SYP";
        basePrice: number;
    }, {
        type: "fixed" | "custom" | "hourly" | "package";
        basePrice: number;
        currency?: "USD" | "SYP" | undefined;
    }>;
    deliveryTime: z.ZodNumber;
    revisions: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    description: {
        ar: string;
        en?: string | undefined;
    };
    tags: string[];
    title: {
        ar: string;
        en?: string | undefined;
    };
    categoryId: string;
    pricing: {
        type: "fixed" | "custom" | "hourly" | "package";
        currency: "USD" | "SYP";
        basePrice: number;
    };
    deliveryTime: number;
    revisions: number;
}, {
    description: {
        ar: string;
        en?: string | undefined;
    };
    tags: string[];
    title: {
        ar: string;
        en?: string | undefined;
    };
    categoryId: string;
    pricing: {
        type: "fixed" | "custom" | "hourly" | "package";
        basePrice: number;
        currency?: "USD" | "SYP" | undefined;
    };
    deliveryTime: number;
    revisions: number;
}>;
export declare const bookingSchema: z.ZodObject<{
    serviceId: z.ZodString;
    packageId: z.ZodOptional<z.ZodString>;
    requirements: z.ZodArray<z.ZodObject<{
        questionId: z.ZodString;
        answer: z.ZodUnion<[z.ZodString, z.ZodArray<z.ZodString, "many">]>;
    }, "strip", z.ZodTypeAny, {
        questionId: string;
        answer: string | string[];
    }, {
        questionId: string;
        answer: string | string[];
    }>, "many">;
    customInstructions: z.ZodOptional<z.ZodString>;
    preferredDeliveryDate: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    serviceId: string;
    requirements: {
        questionId: string;
        answer: string | string[];
    }[];
    packageId?: string | undefined;
    customInstructions?: string | undefined;
    preferredDeliveryDate?: Date | undefined;
}, {
    serviceId: string;
    requirements: {
        questionId: string;
        answer: string | string[];
    }[];
    packageId?: string | undefined;
    customInstructions?: string | undefined;
    preferredDeliveryDate?: Date | undefined;
}>;
export declare const messageSchema: z.ZodObject<{
    content: z.ZodString;
    type: z.ZodDefault<z.ZodEnum<["text", "file", "image"]>>;
}, "strip", z.ZodTypeAny, {
    type: "text" | "file" | "image";
    content: string;
}, {
    content: string;
    type?: "text" | "file" | "image" | undefined;
}>;
export declare const reviewSchema: z.ZodObject<{
    rating: z.ZodNumber;
    title: z.ZodOptional<z.ZodString>;
    comment: z.ZodOptional<z.ZodString>;
    wouldRecommend: z.ZodBoolean;
}, "strip", z.ZodTypeAny, {
    rating: number;
    wouldRecommend: boolean;
    title?: string | undefined;
    comment?: string | undefined;
}, {
    rating: number;
    wouldRecommend: boolean;
    title?: string | undefined;
    comment?: string | undefined;
}>;
export declare const fileUploadSchema: z.ZodObject<{
    filename: z.ZodString;
    mimeType: z.ZodString;
    size: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    filename: string;
    mimeType: string;
    size: number;
}, {
    filename: string;
    mimeType: string;
    size: number;
}>;
export declare const validateEmail: (email: string) => boolean;
export declare const validatePassword: (password: string) => boolean;
export declare const validatePhone: (phone: string) => boolean;
export declare const validateUrl: (url: string) => boolean;
export declare const validateSlug: (slug: string) => boolean;
export declare const validateArabicText: (text: string) => boolean;
export declare const validateEnglishText: (text: string) => boolean;
export declare const validateSyrianPhone: (phone: string) => boolean;
export declare const validateCreditCard: (cardNumber: string) => boolean;
export declare const validateCVV: (cvv: string, cardType?: string) => boolean;
export declare const validateExpiryDate: (month: string, year: string) => boolean;
export declare const validateFileType: (filename: string, allowedTypes: string[]) => boolean;
export declare const validateImageFile: (filename: string) => boolean;
export declare const validateDocumentFile: (filename: string) => boolean;
export declare const validateVideoFile: (filename: string) => boolean;
export declare const validateAudioFile: (filename: string) => boolean;
//# sourceMappingURL=validation.d.ts.map