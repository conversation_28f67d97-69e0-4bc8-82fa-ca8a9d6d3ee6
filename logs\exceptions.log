{
  error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
      at new PrismaClient (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\default.js:43:11)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:9:46)
      at Object.<anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:74:16)
      at Module._compile (node:internal/modules/cjs/loader:1554:14)
      at Object.transformer (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:1186)
      at Module.load (node:internal/modules/cjs/loader:1289:32)
      at Function._load (node:internal/modules/cjs/loader:1108:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
      at Module.require (node:internal/modules/cjs/loader:1311:12),
  level: 'error',
  message: 'uncaughtException: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:46)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:74:16)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)',
  stack: 'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:46)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:74:16)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)',
  exception: true,
  date: 'Tue Jun 10 2025 03:46:38 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 15292,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 102543360,
      heapTotal: 22339584,
      heapUsed: 15549624,
      external: 3229647,
      arrayBuffers: 146390
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 337176.89 },
  trace: [
    {
      column: 11,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js',
      function: 'new PrismaClient',
      line: 43,
      method: null,
      native: false
    },
    {
      column: 46,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts',
      function: '<anonymous>',
      line: 9,
      method: null,
      native: false
    },
    {
      column: 16,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts',
      function: null,
      line: 74,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1554,
      method: '_compile',
      native: false
    },
    {
      column: 1186,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'Object.transformer',
      line: 2,
      method: 'transformer',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1289,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1108,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 220,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:46:38'
}
{
  error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
      at new PrismaClient (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\default.js:43:11)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:9:39)
      at Object.<anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:82:1)
      at Module._compile (node:internal/modules/cjs/loader:1554:14)
      at Object.transformer (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:1186)
      at Module.load (node:internal/modules/cjs/loader:1289:32)
      at Function._load (node:internal/modules/cjs/loader:1108:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
      at Module.require (node:internal/modules/cjs/loader:1311:12),
  level: 'error',
  message: 'uncaughtException: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:39)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:82:1)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)',
  stack: 'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:39)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:82:1)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)',
  exception: true,
  date: 'Tue Jun 10 2025 03:51:11 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 24220,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 89985024,
      heapTotal: 22794240,
      heapUsed: 14057672,
      external: 3198732,
      arrayBuffers: 99067
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 337449.875 },
  trace: [
    {
      column: 11,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js',
      function: 'new PrismaClient',
      line: 43,
      method: null,
      native: false
    },
    {
      column: 39,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts',
      function: '<anonymous>',
      line: 9,
      method: null,
      native: false
    },
    {
      column: 1,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts',
      function: null,
      line: 82,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1554,
      method: '_compile',
      native: false
    },
    {
      column: 1186,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'Object.transformer',
      line: 2,
      method: 'transformer',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1289,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1108,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 220,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:51:11'
}
