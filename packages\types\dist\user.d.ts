interface BaseEntity {
    id: string;
    createdAt: Date;
    updatedAt: Date;
}
interface Location {
    governorate: string;
    city: string;
    district?: string;
    coordinates?: {
        latitude: number;
        longitude: number;
    };
}
interface LocalizedString {
    ar: string;
    en?: string;
}
interface FileUpload {
    id: string;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    url: string;
    thumbnailUrl?: string;
}
export interface User extends BaseEntity {
    email: string;
    phone?: string;
    firstName: string;
    lastName: string;
    avatar?: FileUpload;
    role: UserRole;
    status: UserStatus;
    language: 'ar' | 'en';
    location?: Location;
    emailVerified: boolean;
    phoneVerified: boolean;
    lastLoginAt?: Date;
    profile?: ExpertProfile | ClientProfile;
}
export type UserRole = 'client' | 'expert' | 'admin';
export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending_verification';
export interface UserRegistration {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
    role: 'client' | 'expert';
    language?: 'ar' | 'en';
    location?: Partial<Location>;
    acceptTerms: boolean;
}
export interface UserLogin {
    email: string;
    password: string;
    rememberMe?: boolean;
}
export interface UserProfile {
    firstName: string;
    lastName: string;
    phone?: string;
    avatar?: FileUpload;
    location?: Location;
    language: 'ar' | 'en';
    bio?: LocalizedString;
    website?: string;
    socialLinks?: {
        facebook?: string;
        twitter?: string;
        linkedin?: string;
        instagram?: string;
    };
}
export interface ExpertProfile extends UserProfile {
    title: LocalizedString;
    description: LocalizedString;
    skills: string[];
    experience: ExperienceLevel;
    education?: Education[];
    certifications?: Certification[];
    portfolio?: PortfolioItem[];
    languages: LanguageSkill[];
    hourlyRate?: number;
    availability: Availability;
    responseTime: ResponseTime;
    completedProjects: number;
    rating: number;
    reviewCount: number;
    verified: boolean;
    verificationDocuments?: FileUpload[];
}
export interface ClientProfile extends UserProfile {
    companyName?: string;
    companySize?: CompanySize;
    industry?: string;
    projectsPosted: number;
    totalSpent: number;
    rating: number;
    reviewCount: number;
}
export type ExperienceLevel = 'beginner' | 'intermediate' | 'expert' | 'senior';
export type CompanySize = 'individual' | 'small' | 'medium' | 'large' | 'enterprise';
export interface Education {
    id: string;
    institution: string;
    degree: string;
    field: string;
    startYear: number;
    endYear?: number;
    current: boolean;
    description?: string;
}
export interface Certification {
    id: string;
    name: string;
    issuer: string;
    issueDate: Date;
    expiryDate?: Date;
    credentialId?: string;
    credentialUrl?: string;
    verified: boolean;
}
export interface PortfolioItem {
    id: string;
    title: LocalizedString;
    description: LocalizedString;
    images: FileUpload[];
    category: string;
    tags: string[];
    projectUrl?: string;
    completedAt: Date;
    featured: boolean;
}
export interface LanguageSkill {
    language: string;
    proficiency: LanguageProficiency;
    native: boolean;
}
export type LanguageProficiency = 'basic' | 'conversational' | 'fluent' | 'native';
export interface Availability {
    hoursPerWeek: number;
    timezone: string;
    workingHours: {
        start: string;
        end: string;
    };
    workingDays: WeekDay[];
    unavailableDates?: Date[];
}
export type WeekDay = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
export type ResponseTime = 'within_hour' | 'within_day' | 'within_week';
export interface AuthTokens {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
}
export interface PasswordReset {
    email: string;
    token: string;
    newPassword: string;
}
export interface EmailVerification {
    email: string;
    token: string;
}
export interface PhoneVerification {
    phone: string;
    code: string;
}
export {};
//# sourceMappingURL=user.d.ts.map