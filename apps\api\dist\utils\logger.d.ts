import winston from 'winston';
export declare const logger: winston.Logger;
export declare const requestLogger: (req: any, res: any, next: any) => void;
export declare const logError: (error: Error, context?: any) => void;
export declare const logSecurityEvent: (event: string, details: any, req?: any) => void;
export declare const logPerformance: (operation: string, duration: number, metadata?: any) => void;
export declare const logDatabaseOperation: (operation: string, table: string, duration: number, error?: Error) => void;
export declare const logApiResponse: (endpoint: string, statusCode: number, duration: number, userId?: string) => void;
export declare const logUserAction: (action: string, userId: string, details?: any) => void;
export default logger;
//# sourceMappingURL=logger.d.ts.map