"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createError = exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = exports.AppError = void 0;
const zod_1 = require("zod");
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
// Custom error class
class AppError extends Error {
    statusCode;
    code;
    isOperational;
    details;
    constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', isOperational = true, details) {
        super(message);
        this.statusCode = statusCode;
        this.code = code;
        this.isOperational = isOperational;
        this.details = details;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
/**
 * Handle Prisma errors
 */
const handlePrismaError = (error) => {
    // Check if it's a Prisma error by looking for the code property
    if (error.code) {
        switch (error.code) {
            case 'P2002':
                // Unique constraint violation
                const field = error.meta?.target;
                const fieldName = field?.[0] || 'field';
                return new AppError(`${fieldName} already exists`, 409, 'DUPLICATE_ENTRY', true, { field: fieldName });
            case 'P2025':
                // Record not found
                return new AppError('Record not found', 404, 'NOT_FOUND');
            case 'P2003':
                // Foreign key constraint violation
                return new AppError('Related record not found', 400, 'FOREIGN_KEY_VIOLATION');
            case 'P2014':
                // Required relation violation
                return new AppError('Required relation missing', 400, 'REQUIRED_RELATION_MISSING');
            case 'P2021':
                // Table does not exist
                return new AppError('Database table not found', 500, 'TABLE_NOT_FOUND');
            case 'P2022':
                // Column does not exist
                return new AppError('Database column not found', 500, 'COLUMN_NOT_FOUND');
            default:
                return new AppError('Database operation failed', 500, 'DATABASE_ERROR', true, { prismaCode: error.code });
        }
    }
    return new AppError('Database operation failed', 500, 'DATABASE_ERROR');
};
/**
 * Handle Zod validation errors
 */
const handleZodError = (error) => {
    const errors = error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
    }));
    return new AppError('Validation failed', 400, 'VALIDATION_ERROR', true, { errors });
};
/**
 * Handle JWT errors
 */
const handleJWTError = (error) => {
    if (error.name === 'TokenExpiredError') {
        return new AppError('Token has expired', 401, 'TOKEN_EXPIRED');
    }
    if (error.name === 'JsonWebTokenError') {
        return new AppError('Invalid token', 401, 'TOKEN_INVALID');
    }
    if (error.name === 'NotBeforeError') {
        return new AppError('Token not active yet', 401, 'TOKEN_NOT_ACTIVE');
    }
    return new AppError('Authentication failed', 401, 'AUTH_ERROR');
};
/**
 * Handle Multer errors (file upload)
 */
const handleMulterError = (error) => {
    if (error.code === 'LIMIT_FILE_SIZE') {
        return new AppError('File size too large', 400, 'FILE_TOO_LARGE', true, { maxSize: error.limit });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
        return new AppError('Too many files', 400, 'TOO_MANY_FILES', true, { maxCount: error.limit });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
        return new AppError('Unexpected file field', 400, 'UNEXPECTED_FILE_FIELD', true, { fieldName: error.field });
    }
    return new AppError('File upload failed', 400, 'FILE_UPLOAD_ERROR');
};
/**
 * Send error response
 */
const sendErrorResponse = (error, req, res) => {
    const response = {
        success: false,
        message: error.message,
        code: error.code,
        timestamp: new Date().toISOString(),
        path: req.path,
        method: req.method,
    };
    // Add details in development or for operational errors
    if (config_1.isDevelopment || error.isOperational) {
        if (error.details) {
            response.details = error.details;
        }
    }
    // Add stack trace in development
    if (config_1.isDevelopment && error.stack) {
        response.stack = error.stack;
    }
    res.status(error.statusCode).json(response);
};
/**
 * Global error handling middleware
 */
const errorHandler = (error, req, res, next) => {
    let appError;
    // Handle known error types
    if (error instanceof AppError) {
        appError = error;
    }
    else if (error.code && error.code.startsWith('P')) {
        // Prisma error (has P-code)
        appError = handlePrismaError(error);
    }
    else if (error instanceof zod_1.ZodError) {
        appError = handleZodError(error);
    }
    else if (error.name?.includes('JWT') || error.name?.includes('Token')) {
        appError = handleJWTError(error);
    }
    else if (error.name === 'MulterError') {
        appError = handleMulterError(error);
    }
    else if (error.name === 'SyntaxError' && 'body' in error) {
        appError = new AppError('Invalid JSON in request body', 400, 'INVALID_JSON');
    }
    else {
        // Unknown error
        appError = new AppError(config_1.isDevelopment ? error.message : 'Internal server error', 500, 'INTERNAL_ERROR', false);
    }
    // Log error
    if (!appError.isOperational || appError.statusCode >= 500) {
        logger_1.logger.error('Application Error', {
            message: error.message,
            stack: error.stack,
            statusCode: appError.statusCode,
            code: appError.code,
            path: req.path,
            method: req.method,
            userId: req.user?.id,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
        });
    }
    else {
        logger_1.logger.warn('Operational Error', {
            message: error.message,
            statusCode: appError.statusCode,
            code: appError.code,
            path: req.path,
            method: req.method,
            userId: req.user?.id,
        });
    }
    sendErrorResponse(appError, req, res);
};
exports.errorHandler = errorHandler;
/**
 * Handle 404 errors (route not found)
 */
const notFoundHandler = (req, res, next) => {
    const error = new AppError(`Route ${req.method} ${req.path} not found`, 404, 'ROUTE_NOT_FOUND');
    next(error);
};
exports.notFoundHandler = notFoundHandler;
/**
 * Async error wrapper - catches async errors and passes them to error handler
 */
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
/**
 * Create error helper functions
 */
exports.createError = {
    badRequest: (message, code = 'BAD_REQUEST', details) => new AppError(message, 400, code, true, details),
    unauthorized: (message = 'Unauthorized', code = 'UNAUTHORIZED') => new AppError(message, 401, code),
    forbidden: (message = 'Forbidden', code = 'FORBIDDEN') => new AppError(message, 403, code),
    notFound: (message = 'Not found', code = 'NOT_FOUND') => new AppError(message, 404, code),
    conflict: (message, code = 'CONFLICT', details) => new AppError(message, 409, code, true, details),
    unprocessableEntity: (message, code = 'UNPROCESSABLE_ENTITY', details) => new AppError(message, 422, code, true, details),
    tooManyRequests: (message = 'Too many requests', code = 'TOO_MANY_REQUESTS') => new AppError(message, 429, code),
    internal: (message = 'Internal server error', code = 'INTERNAL_ERROR') => new AppError(message, 500, code, false),
    serviceUnavailable: (message = 'Service unavailable', code = 'SERVICE_UNAVAILABLE') => new AppError(message, 503, code),
};
exports.default = exports.errorHandler;
//# sourceMappingURL=error.js.map