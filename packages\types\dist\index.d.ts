export interface BaseEntity {
    id: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface PaginationParams {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    errors?: string[];
}
export interface ErrorResponse {
    success: false;
    message: string;
    errors?: string[];
    code?: string;
}
export interface Location {
    governorate: string;
    city: string;
    district?: string;
    coordinates?: {
        latitude: number;
        longitude: number;
    };
}
export type SyrianGovernorate = 'damascus' | 'aleppo' | 'homs' | 'hama' | 'latakia' | 'tartus' | 'idlib' | 'daraa' | 'sweida' | 'quneitra' | 'raqqa' | 'deir_ez_zor' | 'hasaka' | 'damascus_countryside';
export type SupportedLanguage = 'ar' | 'en';
export type SupportedLocale = 'ar-SY' | 'en-US';
export interface LocalizedString {
    ar: string;
    en?: string;
}
export interface FileUpload {
    id: string;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    url: string;
    thumbnailUrl?: string;
}
export interface SearchFilters {
    query?: string;
    category?: string;
    location?: Partial<Location>;
    priceRange?: {
        min: number;
        max: number;
    };
    rating?: number;
    deliveryTime?: number;
    language?: SupportedLanguage;
}
export interface Notification {
    id: string;
    userId: string;
    type: NotificationType;
    title: LocalizedString;
    message: LocalizedString;
    data?: Record<string, any>;
    read: boolean;
    createdAt: Date;
}
export type NotificationType = 'booking_request' | 'booking_accepted' | 'booking_rejected' | 'booking_completed' | 'payment_received' | 'message_received' | 'profile_approved' | 'service_approved' | 'system_announcement';
//# sourceMappingURL=index.d.ts.map