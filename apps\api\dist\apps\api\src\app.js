"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const database_1 = require("@freela/database");
const redis_1 = require("./utils/redis");
// Middleware imports
const security_1 = __importDefault(require("./middleware/security"));
const error_1 = require("./middleware/error");
// Route imports
const auth_1 = __importDefault(require("./routes/auth"));
// Swagger imports
const swagger_jsdoc_1 = __importDefault(require("swagger-jsdoc"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
class App {
    app;
    constructor() {
        this.app = (0, express_1.default)();
        this.initializeMiddleware();
        this.initializeRoutes();
        this.initializeSwagger();
        this.initializeErrorHandling();
    }
    initializeMiddleware() {
        // Trust proxy for accurate IP addresses
        this.app.set('trust proxy', 1);
        // Security middleware
        this.app.use(security_1.default.requestId);
        this.app.use(security_1.default.securityHeaders);
        this.app.use(security_1.default.helmet);
        this.app.use(security_1.default.cors);
        this.app.use(security_1.default.compression);
        this.app.use(security_1.default.suspiciousActivityDetection);
        // Rate limiting
        this.app.use(security_1.default.rateLimit);
        // Request parsing
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        // Request logging
        this.app.use(logger_1.requestLogger);
        // Health check endpoint (before rate limiting)
        this.app.get('/health', this.healthCheck);
    }
    initializeRoutes() {
        const apiVersion = config_1.config.API_VERSION;
        // API routes
        this.app.use(`/api/${apiVersion}/auth`, auth_1.default);
        // Root endpoint
        this.app.get('/', (req, res) => {
            res.json({
                success: true,
                message: 'Freela Syria API Server',
                version: apiVersion,
                timestamp: new Date().toISOString(),
                environment: config_1.config.NODE_ENV,
            });
        });
        // API info endpoint
        this.app.get(`/api/${apiVersion}`, (req, res) => {
            res.json({
                success: true,
                message: 'Freela Syria API',
                version: apiVersion,
                endpoints: {
                    auth: `/api/${apiVersion}/auth`,
                    docs: `/api/${apiVersion}/docs`,
                    health: '/health',
                },
                timestamp: new Date().toISOString(),
            });
        });
    }
    initializeSwagger() {
        if (!config_1.isDevelopment)
            return;
        const swaggerOptions = {
            definition: {
                openapi: '3.0.0',
                info: {
                    title: 'Freela Syria API',
                    version: '1.0.0',
                    description: 'AI-Powered Freelance Marketplace API for Syrian Experts',
                    contact: {
                        name: 'Freela Syria Team',
                        email: '<EMAIL>',
                    },
                    license: {
                        name: 'MIT',
                        url: 'https://opensource.org/licenses/MIT',
                    },
                },
                servers: [
                    {
                        url: `http://localhost:${config_1.config.PORT}/api/${config_1.config.API_VERSION}`,
                        description: 'Development server',
                    },
                ],
                components: {
                    securitySchemes: {
                        bearerAuth: {
                            type: 'http',
                            scheme: 'bearer',
                            bearerFormat: 'JWT',
                        },
                    },
                },
                security: [
                    {
                        bearerAuth: [],
                    },
                ],
                tags: [
                    {
                        name: 'Authentication',
                        description: 'User authentication and authorization endpoints',
                    },
                    {
                        name: 'Users',
                        description: 'User management endpoints',
                    },
                    {
                        name: 'Experts',
                        description: 'Expert profile management endpoints',
                    },
                    {
                        name: 'Services',
                        description: 'Service management endpoints',
                    },
                    {
                        name: 'Bookings',
                        description: 'Booking management endpoints',
                    },
                    {
                        name: 'Payments',
                        description: 'Payment processing endpoints',
                    },
                    {
                        name: 'Chat',
                        description: 'Messaging and chat endpoints',
                    },
                    {
                        name: 'Admin',
                        description: 'Administrative endpoints',
                    },
                ],
            },
            apis: ['./src/routes/*.ts'], // Path to the API docs
        };
        const swaggerSpec = (0, swagger_jsdoc_1.default)(swaggerOptions);
        // Swagger UI
        this.app.use(`/api/${config_1.config.API_VERSION}/docs`, swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swaggerSpec, {
            explorer: true,
            customCss: '.swagger-ui .topbar { display: none }',
            customSiteTitle: 'Freela Syria API Documentation',
        }));
        // Swagger JSON
        this.app.get(`/api/${config_1.config.API_VERSION}/docs.json`, (req, res) => {
            res.setHeader('Content-Type', 'application/json');
            res.send(swaggerSpec);
        });
        logger_1.logger.info(`Swagger documentation available at http://localhost:${config_1.config.PORT}/api/${config_1.config.API_VERSION}/docs`);
    }
    initializeErrorHandling() {
        // 404 handler
        this.app.use(error_1.notFoundHandler);
        // Global error handler
        this.app.use(error_1.errorHandler);
    }
    healthCheck = async (req, res) => {
        try {
            // Check database connection
            const dbHealth = await this.checkDatabaseHealth();
            // Check Redis connection
            const redisHealth = await this.checkRedisHealth();
            const health = {
                status: 'ok',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: config_1.config.NODE_ENV,
                version: config_1.config.API_VERSION,
                services: {
                    database: dbHealth ? 'healthy' : 'unhealthy',
                    redis: redisHealth ? 'healthy' : 'unhealthy',
                },
                memory: {
                    used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
                    total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
                },
            };
            const statusCode = dbHealth && redisHealth ? 200 : 503;
            res.status(statusCode).json(health);
        }
        catch (error) {
            logger_1.logger.error('Health check failed', { error });
            res.status(503).json({
                status: 'error',
                timestamp: new Date().toISOString(),
                message: 'Health check failed',
            });
        }
    };
    async checkDatabaseHealth() {
        try {
            const { checkDatabaseHealth } = await Promise.resolve().then(() => __importStar(require('@freela/database')));
            return await checkDatabaseHealth();
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.debug('Database health check failed', { error: errorMessage });
            return false;
        }
    }
    async checkRedisHealth() {
        try {
            return await redis_1.redis.ping();
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.debug('Redis health check failed', { error: errorMessage });
            return false;
        }
    }
    async initialize() {
        try {
            logger_1.logger.info('🚀 Initializing application...');
            // Try to connect to database
            try {
                await (0, database_1.connectDatabase)();
                logger_1.logger.info('✅ Database connected');
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                logger_1.logger.error('❌ Database connection failed', { error: errorMessage });
                logger_1.logger.warn('⚠️ Application will start without database connection');
            }
            // Try to connect to Redis
            try {
                await redis_1.redis.connect();
                logger_1.logger.info('✅ Redis connected');
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                logger_1.logger.error('❌ Redis connection failed', { error: errorMessage });
                logger_1.logger.warn('⚠️ Application will start without Redis connection');
            }
            logger_1.logger.info('✅ Application initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('❌ Application initialization failed', { error });
            throw error;
        }
    }
    async shutdown() {
        try {
            // Disconnect from Redis
            await redis_1.redis.disconnect();
            logger_1.logger.info('✅ Redis disconnected');
            // Database disconnection is handled by Prisma client
            logger_1.logger.info('✅ Application shutdown completed');
        }
        catch (error) {
            logger_1.logger.error('❌ Application shutdown failed', { error });
            throw error;
        }
    }
}
exports.default = App;
//# sourceMappingURL=app.js.map