"use strict";
// Application constants
Object.defineProperty(exports, "__esModule", { value: true });
exports.ERROR_CODES = exports.NOTIFICATION_TYPES = exports.PASSWORD_RESET = exports.EMAIL_VERIFICATION = exports.SESSION_CONFIG = exports.CACHE_TTL = exports.RATE_LIMITS = exports.TEXT_LIMITS = exports.REVISION_LIMITS = exports.DELIVERY_TIME_RANGES = exports.PRICE_RANGES = exports.RATING_SCALE = exports.PAGINATION_DEFAULTS = exports.FILE_SIZE_LIMITS = exports.ALLOWED_VIDEO_TYPES = exports.ALLOWED_DOCUMENT_TYPES = exports.ALLOWED_IMAGE_TYPES = exports.LANGUAGES = exports.CURRENCIES = exports.PAYMENT_METHODS = exports.PAYMENT_STATUSES = exports.BOOKING_STATUSES = exports.SERVICE_STATUSES = exports.USER_STATUSES = exports.USER_ROLES = exports.SERVICE_CATEGORIES_EN = exports.SERVICE_CATEGORIES = exports.SYRIAN_GOVERNORATES_EN = exports.SYRIAN_GOVERNORATES = void 0;
// Syrian governorates
exports.SYRIAN_GOVERNORATES = [
    'دمشق',
    'ريف دمشق',
    'حلب',
    'حمص',
    'حماة',
    'اللاذقية',
    'إدلب',
    'الحسكة',
    'دير الزور',
    'الرقة',
    'درعا',
    'السويداء',
    'القنيطرة',
    'طرطوس',
];
exports.SYRIAN_GOVERNORATES_EN = [
    'Damascus',
    'Damascus Countryside',
    'Aleppo',
    'Homs',
    'Hama',
    'Latakia',
    'Idlib',
    'Al-Hasakah',
    'Deir ez-Zor',
    'Raqqa',
    'Daraa',
    'As-Suwayda',
    'Quneitra',
    'Tartus',
];
// Service categories
exports.SERVICE_CATEGORIES = [
    'تطوير البرمجيات',
    'التصميم الجرافيكي',
    'التسويق الرقمي',
    'الكتابة والترجمة',
    'التصوير والفيديو',
    'الاستشارات',
    'التعليم والتدريب',
    'الخدمات المالية',
    'الهندسة والعمارة',
    'الطب والصحة',
];
exports.SERVICE_CATEGORIES_EN = [
    'Software Development',
    'Graphic Design',
    'Digital Marketing',
    'Writing & Translation',
    'Photography & Video',
    'Consulting',
    'Education & Training',
    'Financial Services',
    'Engineering & Architecture',
    'Medicine & Health',
];
// User roles
exports.USER_ROLES = ['CLIENT', 'EXPERT', 'ADMIN'];
// User statuses
exports.USER_STATUSES = [
    'ACTIVE',
    'INACTIVE',
    'SUSPENDED',
    'PENDING_VERIFICATION',
];
// Service statuses
exports.SERVICE_STATUSES = [
    'DRAFT',
    'PENDING_REVIEW',
    'ACTIVE',
    'PAUSED',
    'REJECTED',
    'ARCHIVED',
];
// Booking statuses
exports.BOOKING_STATUSES = [
    'PENDING',
    'ACCEPTED',
    'IN_PROGRESS',
    'DELIVERED',
    'REVISION_REQUESTED',
    'COMPLETED',
    'CANCELLED',
    'DISPUTED',
    'REFUNDED',
];
// Payment statuses
exports.PAYMENT_STATUSES = [
    'PENDING',
    'PROCESSING',
    'COMPLETED',
    'FAILED',
    'CANCELLED',
    'REFUNDED',
    'DISPUTED',
    'CHARGEBACK',
];
// Payment methods
exports.PAYMENT_METHODS = [
    'CREDIT_CARD',
    'DEBIT_CARD',
    'PAYPAL',
    'BANK_TRANSFER',
    'MOBILE_WALLET',
    'CRYPTOCURRENCY',
    'CASH',
];
// Currencies
exports.CURRENCIES = ['USD', 'SYP'];
// Languages
exports.LANGUAGES = ['ar', 'en'];
// File types
exports.ALLOWED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
];
exports.ALLOWED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
];
exports.ALLOWED_VIDEO_TYPES = [
    'video/mp4',
    'video/avi',
    'video/mov',
    'video/wmv',
    'video/webm',
];
// File size limits (in bytes)
exports.FILE_SIZE_LIMITS = {
    AVATAR: 5 * 1024 * 1024, // 5MB
    PORTFOLIO: 10 * 1024 * 1024, // 10MB
    DOCUMENT: 20 * 1024 * 1024, // 20MB
    VIDEO: 100 * 1024 * 1024, // 100MB
};
// Pagination defaults
exports.PAGINATION_DEFAULTS = {
    PAGE: 1,
    LIMIT: 10,
    MAX_LIMIT: 100,
};
// Rating scale
exports.RATING_SCALE = {
    MIN: 1,
    MAX: 5,
};
// Price ranges
exports.PRICE_RANGES = {
    MIN_USD: 5,
    MAX_USD: 10000,
    MIN_SYP: 10000,
    MAX_SYP: 20000000,
};
// Delivery time ranges (in days)
exports.DELIVERY_TIME_RANGES = {
    MIN: 1,
    MAX: 365,
};
// Revision limits
exports.REVISION_LIMITS = {
    MIN: 0,
    MAX: 10,
};
// Text length limits
exports.TEXT_LIMITS = {
    TITLE_MIN: 10,
    TITLE_MAX: 100,
    DESCRIPTION_MIN: 50,
    DESCRIPTION_MAX: 2000,
    BIO_MAX: 500,
    MESSAGE_MAX: 2000,
    REVIEW_TITLE_MAX: 100,
    REVIEW_COMMENT_MAX: 1000,
};
// API rate limits
exports.RATE_LIMITS = {
    GENERAL: {
        WINDOW_MS: 15 * 60 * 1000, // 15 minutes
        MAX_REQUESTS: 100,
    },
    AUTH: {
        WINDOW_MS: 15 * 60 * 1000, // 15 minutes
        MAX_REQUESTS: 5,
    },
    UPLOAD: {
        WINDOW_MS: 60 * 1000, // 1 minute
        MAX_REQUESTS: 10,
    },
};
// Cache TTL (in seconds)
exports.CACHE_TTL = {
    USER_DATA: 3600, // 1 hour
    SERVICE_DATA: 1800, // 30 minutes
    CATEGORY_DATA: 86400, // 24 hours
    SEARCH_RESULTS: 300, // 5 minutes
};
// Session configuration
exports.SESSION_CONFIG = {
    ACCESS_TOKEN_EXPIRES: 15 * 60, // 15 minutes
    REFRESH_TOKEN_EXPIRES: 7 * 24 * 60 * 60, // 7 days
    REMEMBER_ME_EXPIRES: 30 * 24 * 60 * 60, // 30 days
};
// Email verification
exports.EMAIL_VERIFICATION = {
    TOKEN_EXPIRES: 24 * 60 * 60, // 24 hours
    RESEND_COOLDOWN: 5 * 60, // 5 minutes
};
// Password reset
exports.PASSWORD_RESET = {
    TOKEN_EXPIRES: 60 * 60, // 1 hour
    REQUEST_COOLDOWN: 5 * 60, // 5 minutes
};
// Notification types
exports.NOTIFICATION_TYPES = [
    'BOOKING_CREATED',
    'BOOKING_ACCEPTED',
    'BOOKING_REJECTED',
    'BOOKING_COMPLETED',
    'BOOKING_CANCELLED',
    'PAYMENT_RECEIVED',
    'PAYMENT_FAILED',
    'MESSAGE_RECEIVED',
    'REVIEW_RECEIVED',
    'SERVICE_APPROVED',
    'SERVICE_REJECTED',
    'ACCOUNT_VERIFIED',
    'ACCOUNT_SUSPENDED',
];
// Error codes
exports.ERROR_CODES = {
    // Authentication
    INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
    TOKEN_EXPIRED: 'TOKEN_EXPIRED',
    TOKEN_INVALID: 'TOKEN_INVALID',
    SESSION_EXPIRED: 'SESSION_EXPIRED',
    // Authorization
    INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
    ACCESS_DENIED: 'ACCESS_DENIED',
    // Validation
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    REQUIRED_FIELD: 'REQUIRED_FIELD',
    INVALID_FORMAT: 'INVALID_FORMAT',
    // Resources
    NOT_FOUND: 'NOT_FOUND',
    ALREADY_EXISTS: 'ALREADY_EXISTS',
    DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
    // Rate limiting
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
    // File upload
    FILE_TOO_LARGE: 'FILE_TOO_LARGE',
    INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
    // Payment
    PAYMENT_FAILED: 'PAYMENT_FAILED',
    INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
    // General
    INTERNAL_ERROR: 'INTERNAL_ERROR',
    SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
};
//# sourceMappingURL=constants.js.map