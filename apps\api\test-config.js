const dotenv = require('dotenv');
const path = require('path');

console.log('Current working directory:', process.cwd());
console.log('Loading .env file...');

// Load .env file
const result = dotenv.config();

if (result.error) {
  console.error('Error loading .env file:', result.error);
} else {
  console.log('✅ .env file loaded successfully');
}

console.log('Environment variables:');
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');
console.log('REDIS_URL:', process.env.REDIS_URL ? 'Set' : 'Not set');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'Set' : 'Not set');
console.log('SMTP_USER:', process.env.SMTP_USER);
console.log('FROM_EMAIL:', process.env.FROM_EMAIL);
console.log('PORT:', process.env.PORT);
console.log('NODE_ENV:', process.env.NODE_ENV);

// Test config loading
try {
  console.log('\nTesting config validation...');
  require('./dist/config/index.js');
  console.log('✅ Config validation passed!');
} catch (error) {
  console.error('❌ Config validation failed:', error.message);
}
