export interface JWTPayload {
    userId: string;
    email: string;
    role: string;
    sessionId: string;
    iat?: number;
    exp?: number;
}
export interface TokenPair {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    refreshExpiresIn: number;
}
export declare const passwordUtils: {
    /**
     * Hash a password using bcrypt
     */
    hash(password: string): Promise<string>;
    /**
     * Verify a password against its hash
     */
    verify(password: string, hash: string): Promise<boolean>;
    /**
     * Generate a secure random password
     */
    generateSecure(length?: number): string;
};
export declare const jwtUtils: {
    /**
     * Generate access token
     */
    generateAccessToken(payload: Omit<JWTPayload, "iat" | "exp">): string;
    /**
     * Generate refresh token
     */
    generateRefreshToken(payload: Omit<JWTPayload, "iat" | "exp">): string;
    /**
     * Verify access token
     */
    verifyAccessToken(token: string): JWTPayload | null;
    /**
     * Verify refresh token
     */
    verifyRefreshToken(token: string): JWTPayload | null;
    /**
     * Decode token without verification (for debugging)
     */
    decode(token: string): JWTPayload | null;
};
export declare const sessionUtils: {
    /**
     * Create a new session
     */
    createSession(userId: string, email: string, role: string, userData?: any): Promise<TokenPair>;
    /**
     * Refresh tokens using refresh token
     */
    refreshTokens(refreshToken: string): Promise<TokenPair | null>;
    /**
     * Validate session
     */
    validateSession(sessionId: string): Promise<boolean>;
    /**
     * Invalidate session (logout)
     */
    invalidateSession(sessionId: string): Promise<boolean>;
    /**
     * Invalidate all user sessions
     */
    invalidateAllUserSessions(userId: string): Promise<boolean>;
    /**
     * Update session activity
     */
    updateSessionActivity(sessionId: string): Promise<boolean>;
};
export declare const authUtils: {
    /**
     * Extract token from Authorization header
     */
    extractTokenFromHeader(authHeader: string | undefined): string | null;
    /**
     * Generate verification code
     */
    generateVerificationCode(length?: number): string;
    /**
     * Generate secure token for password reset, email verification, etc.
     */
    generateSecureToken(): string;
    /**
     * Check if password meets security requirements
     */
    validatePasswordStrength(password: string): {
        isValid: boolean;
        errors: string[];
    };
};
//# sourceMappingURL=auth.d.ts.map