{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/utils/auth.ts"], "names": [], "mappings": "AAQA,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,MAAM,CAAC;IAClB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AAGD,MAAM,WAAW,SAAS;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,gBAAgB,EAAE,MAAM,CAAC;CAC1B;AAGD,eAAO,MAAM,aAAa;IACxB;;OAEG;mBACkB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAS7C;;OAEG;qBACoB,MAAM,QAAQ,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAS9D;;OAEG;4BACoB,MAAM,GAAQ,MAAM;CAkB5C,CAAC;AAGF,eAAO,MAAM,QAAQ;IACnB;;OAEG;iCAC0B,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,KAAK,CAAC,GAAG,MAAM;IAarE;;OAEG;kCAC2B,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,KAAK,CAAC,GAAG,MAAM;IAatE;;OAEG;6BACsB,MAAM,GAAG,UAAU,GAAG,IAAI;IAkBnD;;OAEG;8BACuB,MAAM,GAAG,UAAU,GAAG,IAAI;IAkBpD;;OAEG;kBACW,MAAM,GAAG,UAAU,GAAG,IAAI;CAQzC,CAAC;AAGF,eAAO,MAAM,YAAY;IACvB;;OAEG;0BACyB,MAAM,SAAS,MAAM,QAAQ,MAAM,aAAa,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC;IAsCpG;;OAEG;gCAC+B,MAAM,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;IAuDpE;;OAEG;+BAC8B,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAU1D;;OAEG;iCACgC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAgB5D;;OAEG;sCACqC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAYjE;;OAEG;qCACoC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;CAgBjE,CAAC;AAGF,eAAO,MAAM,SAAS;IACpB;;OAEG;uCACgC,MAAM,GAAG,SAAS,GAAG,MAAM,GAAG,IAAI;IAOrE;;OAEG;sCAC8B,MAAM,GAAO,MAAM;IASpD;;OAEG;2BACoB,MAAM;IAI7B;;OAEG;uCACgC,MAAM,GAAG;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE;CA4BnF,CAAC"}