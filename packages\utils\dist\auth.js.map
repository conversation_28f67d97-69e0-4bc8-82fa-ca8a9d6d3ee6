{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../src/auth.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,sDAAsD;AACtD,+CAA6B;AAE7B,qCAAqC;AAC9B,MAAM,oBAAoB,GAAG,CAAC,SAAiB,EAAE,EAAU,EAAE;IAClE,MAAM,KAAK,GAAG,gEAAgE,CAAC;IAC/E,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAChC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACnE,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAPW,QAAA,oBAAoB,wBAO/B;AAEK,MAAM,mBAAmB,GAAG,CAAC,SAAiB,CAAC,EAAU,EAAE;IAChE,MAAM,KAAK,GAAG,YAAY,CAAC;IAC3B,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAChC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACnE,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAPW,QAAA,mBAAmB,uBAO9B;AAEK,MAAM,YAAY,GAAG,CAAC,KAAa,EAAW,EAAE;IACrD,MAAM,UAAU,GAAG,4BAA4B,CAAC;IAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC,CAAC;AAHW,QAAA,YAAY,gBAGvB;AAEK,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;IACtD,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;AACpC,CAAC,CAAC;AAFW,QAAA,cAAc,kBAEzB;AAEK,MAAM,gBAAgB,GAAG,CAAC,QAAgB,EAAW,EAAE;IAC5D,4EAA4E;IAC5E,MAAM,mBAAmB,GAAG,sEAAsE,CAAC;IACnG,OAAO,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5C,CAAC,CAAC;AAJW,QAAA,gBAAgB,oBAI3B;AAEK,MAAM,aAAa,GAAG,CAAC,KAAa,EAAU,EAAE;IACrD,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC3C,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB"}