export declare const SYRIAN_GOVERNORATES: readonly ["دمشق", "ريف دمشق", "حلب", "حمص", "حماة", "اللاذقية", "إدلب", "الحسكة", "دير الزور", "الرقة", "درعا", "السويداء", "القنيطرة", "طرطوس"];
export declare const SYRIAN_GOVERNORATES_EN: readonly ["Damascus", "Damascus Countryside", "Aleppo", "Homs", "Hama", "Latakia", "Idl<PERSON>", "Al-Hasakah", "Deir ez-Zor", "<PERSON>q<PERSON>", "Daraa", "As-Suwayda", "Quneitra", "Tartus"];
export declare const SERVICE_CATEGORIES: readonly ["تطوير البرمجيات", "التصميم الجرافيكي", "التسويق الرقمي", "الكتابة والترجمة", "التصوير والفيديو", "الاستشارات", "التعليم والتدريب", "الخدمات المالية", "الهندسة والعمارة", "الطب والصحة"];
export declare const SERVICE_CATEGORIES_EN: readonly ["Software Development", "Graphic Design", "Digital Marketing", "Writing & Translation", "Photography & Video", "Consulting", "Education & Training", "Financial Services", "Engineering & Architecture", "Medicine & Health"];
export declare const USER_ROLES: readonly ["CLIENT", "EXPERT", "ADMIN"];
export declare const USER_STATUSES: readonly ["ACTIVE", "INACTIVE", "SUSPENDED", "PENDING_VERIFICATION"];
export declare const SERVICE_STATUSES: readonly ["DRAFT", "PENDING_REVIEW", "ACTIVE", "PAUSED", "REJECTED", "ARCHIVED"];
export declare const BOOKING_STATUSES: readonly ["PENDING", "ACCEPTED", "IN_PROGRESS", "DELIVERED", "REVISION_REQUESTED", "COMPLETED", "CANCELLED", "DISPUTED", "REFUNDED"];
export declare const PAYMENT_STATUSES: readonly ["PENDING", "PROCESSING", "COMPLETED", "FAILED", "CANCELLED", "REFUNDED", "DISPUTED", "CHARGEBACK"];
export declare const PAYMENT_METHODS: readonly ["CREDIT_CARD", "DEBIT_CARD", "PAYPAL", "BANK_TRANSFER", "MOBILE_WALLET", "CRYPTOCURRENCY", "CASH"];
export declare const CURRENCIES: readonly ["USD", "SYP"];
export declare const LANGUAGES: readonly ["ar", "en"];
export declare const ALLOWED_IMAGE_TYPES: readonly ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
export declare const ALLOWED_DOCUMENT_TYPES: readonly ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain"];
export declare const ALLOWED_VIDEO_TYPES: readonly ["video/mp4", "video/avi", "video/mov", "video/wmv", "video/webm"];
export declare const FILE_SIZE_LIMITS: {
    readonly AVATAR: number;
    readonly PORTFOLIO: number;
    readonly DOCUMENT: number;
    readonly VIDEO: number;
};
export declare const PAGINATION_DEFAULTS: {
    readonly PAGE: 1;
    readonly LIMIT: 10;
    readonly MAX_LIMIT: 100;
};
export declare const RATING_SCALE: {
    readonly MIN: 1;
    readonly MAX: 5;
};
export declare const PRICE_RANGES: {
    readonly MIN_USD: 5;
    readonly MAX_USD: 10000;
    readonly MIN_SYP: 10000;
    readonly MAX_SYP: 20000000;
};
export declare const DELIVERY_TIME_RANGES: {
    readonly MIN: 1;
    readonly MAX: 365;
};
export declare const REVISION_LIMITS: {
    readonly MIN: 0;
    readonly MAX: 10;
};
export declare const TEXT_LIMITS: {
    readonly TITLE_MIN: 10;
    readonly TITLE_MAX: 100;
    readonly DESCRIPTION_MIN: 50;
    readonly DESCRIPTION_MAX: 2000;
    readonly BIO_MAX: 500;
    readonly MESSAGE_MAX: 2000;
    readonly REVIEW_TITLE_MAX: 100;
    readonly REVIEW_COMMENT_MAX: 1000;
};
export declare const RATE_LIMITS: {
    readonly GENERAL: {
        readonly WINDOW_MS: number;
        readonly MAX_REQUESTS: 100;
    };
    readonly AUTH: {
        readonly WINDOW_MS: number;
        readonly MAX_REQUESTS: 5;
    };
    readonly UPLOAD: {
        readonly WINDOW_MS: number;
        readonly MAX_REQUESTS: 10;
    };
};
export declare const CACHE_TTL: {
    readonly USER_DATA: 3600;
    readonly SERVICE_DATA: 1800;
    readonly CATEGORY_DATA: 86400;
    readonly SEARCH_RESULTS: 300;
};
export declare const SESSION_CONFIG: {
    readonly ACCESS_TOKEN_EXPIRES: number;
    readonly REFRESH_TOKEN_EXPIRES: number;
    readonly REMEMBER_ME_EXPIRES: number;
};
export declare const EMAIL_VERIFICATION: {
    readonly TOKEN_EXPIRES: number;
    readonly RESEND_COOLDOWN: number;
};
export declare const PASSWORD_RESET: {
    readonly TOKEN_EXPIRES: number;
    readonly REQUEST_COOLDOWN: number;
};
export declare const NOTIFICATION_TYPES: readonly ["BOOKING_CREATED", "BOOKING_ACCEPTED", "BOOKING_REJECTED", "BOOKING_COMPLETED", "BOOKING_CANCELLED", "PAYMENT_RECEIVED", "PAYMENT_FAILED", "MESSAGE_RECEIVED", "REVIEW_RECEIVED", "SERVICE_APPROVED", "SERVICE_REJECTED", "ACCOUNT_VERIFIED", "ACCOUNT_SUSPENDED"];
export declare const ERROR_CODES: {
    readonly INVALID_CREDENTIALS: "INVALID_CREDENTIALS";
    readonly TOKEN_EXPIRED: "TOKEN_EXPIRED";
    readonly TOKEN_INVALID: "TOKEN_INVALID";
    readonly SESSION_EXPIRED: "SESSION_EXPIRED";
    readonly INSUFFICIENT_PERMISSIONS: "INSUFFICIENT_PERMISSIONS";
    readonly ACCESS_DENIED: "ACCESS_DENIED";
    readonly VALIDATION_ERROR: "VALIDATION_ERROR";
    readonly REQUIRED_FIELD: "REQUIRED_FIELD";
    readonly INVALID_FORMAT: "INVALID_FORMAT";
    readonly NOT_FOUND: "NOT_FOUND";
    readonly ALREADY_EXISTS: "ALREADY_EXISTS";
    readonly DUPLICATE_ENTRY: "DUPLICATE_ENTRY";
    readonly RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED";
    readonly FILE_TOO_LARGE: "FILE_TOO_LARGE";
    readonly INVALID_FILE_TYPE: "INVALID_FILE_TYPE";
    readonly PAYMENT_FAILED: "PAYMENT_FAILED";
    readonly INSUFFICIENT_FUNDS: "INSUFFICIENT_FUNDS";
    readonly INTERNAL_ERROR: "INTERNAL_ERROR";
    readonly SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE";
};
//# sourceMappingURL=constants.d.ts.map