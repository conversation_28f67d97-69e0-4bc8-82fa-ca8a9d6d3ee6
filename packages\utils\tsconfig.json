{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "noEmit": false, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}