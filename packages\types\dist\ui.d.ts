export interface ComponentProps {
    className?: string;
    style?: Record<string, any>;
    children?: any;
    testId?: string;
}
export interface Theme {
    colors: ColorPalette;
    typography: Typography;
    spacing: Spacing;
    breakpoints: Breakpoints;
    shadows: Shadows;
    borderRadius: BorderRadius;
    zIndex: ZIndex;
}
export interface ColorPalette {
    primary: ColorScale;
    secondary: ColorScale;
    success: ColorScale;
    warning: ColorScale;
    error: ColorScale;
    info: ColorScale;
    neutral: ColorScale;
    background: BackgroundColors;
    text: TextColors;
    border: BorderColors;
}
export interface ColorScale {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
}
export interface BackgroundColors {
    primary: string;
    secondary: string;
    tertiary: string;
    overlay: string;
    modal: string;
    card: string;
    input: string;
}
export interface TextColors {
    primary: string;
    secondary: string;
    tertiary: string;
    disabled: string;
    inverse: string;
    link: string;
    error: string;
    success: string;
    warning: string;
}
export interface BorderColors {
    primary: string;
    secondary: string;
    tertiary: string;
    focus: string;
    error: string;
    success: string;
    warning: string;
}
export interface Typography {
    fontFamily: FontFamily;
    fontSize: FontSize;
    fontWeight: FontWeight;
    lineHeight: LineHeight;
    letterSpacing: LetterSpacing;
}
export interface FontFamily {
    primary: string;
    secondary: string;
    mono: string;
    arabic: string;
}
export interface FontSize {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    '5xl': string;
    '6xl': string;
}
export interface FontWeight {
    light: number;
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
    extrabold: number;
}
export interface LineHeight {
    tight: number;
    normal: number;
    relaxed: number;
    loose: number;
}
export interface LetterSpacing {
    tighter: string;
    tight: string;
    normal: string;
    wide: string;
    wider: string;
    widest: string;
}
export interface Spacing {
    0: string;
    1: string;
    2: string;
    3: string;
    4: string;
    5: string;
    6: string;
    8: string;
    10: string;
    12: string;
    16: string;
    20: string;
    24: string;
    32: string;
    40: string;
    48: string;
    56: string;
    64: string;
}
export interface Breakpoints {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
}
export interface Shadows {
    sm: string;
    base: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    inner: string;
    none: string;
}
export interface BorderRadius {
    none: string;
    sm: string;
    base: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    full: string;
}
export interface ZIndex {
    hide: number;
    auto: number;
    base: number;
    docked: number;
    dropdown: number;
    sticky: number;
    banner: number;
    overlay: number;
    modal: number;
    popover: number;
    skipLink: number;
    toast: number;
    tooltip: number;
}
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type ComponentVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
export type ComponentColor = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
export type ComponentState = 'default' | 'hover' | 'active' | 'focus' | 'disabled' | 'loading';
export interface FormFieldProps extends ComponentProps {
    label?: string;
    placeholder?: string;
    helperText?: string;
    errorText?: string;
    required?: boolean;
    disabled?: boolean;
    readOnly?: boolean;
    size?: ComponentSize;
    variant?: ComponentVariant;
}
export interface SelectOption {
    value: string | number;
    label: string;
    disabled?: boolean;
    group?: string;
}
export interface LayoutProps extends ComponentProps {
    direction?: 'row' | 'column';
    align?: 'start' | 'center' | 'end' | 'stretch';
    justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
    wrap?: boolean;
    gap?: keyof Spacing;
    padding?: keyof Spacing;
    margin?: keyof Spacing;
}
export interface NavigationItem {
    id: string;
    label: string;
    icon?: string;
    href?: string;
    onClick?: () => void;
    active?: boolean;
    disabled?: boolean;
    badge?: string | number;
    children?: NavigationItem[];
}
export interface ModalProps extends ComponentProps {
    isOpen: boolean;
    onClose: () => void;
    title?: string;
    size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
    closeOnOverlayClick?: boolean;
    closeOnEscape?: boolean;
    showCloseButton?: boolean;
    preventScroll?: boolean;
}
export interface ToastProps {
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title?: string;
    message: string;
    duration?: number;
    action?: {
        label: string;
        onClick: () => void;
    };
    onClose?: () => void;
}
export interface TableColumn<T = any> {
    key: keyof T | string;
    title: string;
    width?: string | number;
    align?: 'left' | 'center' | 'right';
    sortable?: boolean;
    render?: (value: any, record: T, index: number) => any;
}
export interface TableProps<T = any> extends ComponentProps {
    columns: TableColumn<T>[];
    data: T[];
    loading?: boolean;
    pagination?: PaginationProps;
    selection?: TableSelectionProps<T>;
    sorting?: TableSortingProps;
    emptyState?: any;
}
export interface PaginationProps {
    current: number;
    total: number;
    pageSize: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    onChange: (page: number, pageSize: number) => void;
}
export interface TableSelectionProps<T> {
    selectedRowKeys: string[];
    onChange: (selectedRowKeys: string[], selectedRows: T[]) => void;
    getCheckboxProps?: (record: T) => {
        disabled?: boolean;
    };
}
export interface TableSortingProps {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    onChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
}
export interface LoadingState {
    isLoading: boolean;
    error?: string | null;
    data?: any;
}
export interface ResponsiveValue<T> {
    base?: T;
    sm?: T;
    md?: T;
    lg?: T;
    xl?: T;
    '2xl'?: T;
}
export interface AnimationProps {
    duration?: number;
    delay?: number;
    easing?: string;
    direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
    fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
    iterationCount?: number | 'infinite';
    playState?: 'running' | 'paused';
}
export interface AccessibilityProps {
    'aria-label'?: string;
    'aria-labelledby'?: string;
    'aria-describedby'?: string;
    'aria-expanded'?: boolean;
    'aria-hidden'?: boolean;
    'aria-disabled'?: boolean;
    'aria-required'?: boolean;
    'aria-invalid'?: boolean;
    role?: string;
    tabIndex?: number;
}
//# sourceMappingURL=ui.d.ts.map