import { PrismaClient } from '@prisma/client';
declare global {
    var __prisma: PrismaClient | undefined;
}
export declare const prisma: any;
export declare const connectDatabase: () => Promise<void>;
export declare const disconnectDatabase: () => Promise<void>;
export declare const checkDatabaseHealth: () => Promise<boolean>;
export declare const withTransaction: <T>(callback: (tx: PrismaClient) => Promise<T>) => Promise<T>;
export default prisma;
//# sourceMappingURL=client.d.ts.map