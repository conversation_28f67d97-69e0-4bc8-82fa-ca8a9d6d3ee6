declare const PrismaClient: any;
declare global {
    var __prisma: PrismaClient | undefined;
}
declare const prisma: any;
declare const connectDatabase: () => Promise<void>;
declare const disconnectDatabase: () => Promise<void>;
declare const checkDatabaseHealth: () => Promise<boolean>;
declare const withTransaction: <T>(callback: (tx: PrismaClient) => Promise<T>) => Promise<T>;
//# sourceMappingURL=client.d.ts.map