export declare const config: {
    DATABASE_URL: string;
    REDIS_URL: string;
    JWT_SECRET: string;
    JWT_REFRESH_SECRET: string;
    JWT_EXPIRES_IN: string;
    JWT_REFRESH_EXPIRES_IN: string;
    PORT: number;
    NODE_ENV: "development" | "production" | "test";
    API_VERSION: string;
    CORS_ORIGIN: string;
    SMTP_HOST: string;
    SMTP_PORT: number;
    SMTP_USER: string;
    SMTP_PASS: string;
    FROM_EMAIL: string;
    FROM_NAME: string;
    UPLOAD_MAX_SIZE: number;
    UPLOAD_ALLOWED_TYPES: string;
    UPLOAD_PATH: string;
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    BCRYPT_ROUNDS: number;
    SESSION_SECRET: string;
    LOG_LEVEL: "error" | "warn" | "info" | "debug";
    CDN_URL?: string | undefined;
    OPENAI_API_KEY?: string | undefined;
    GOOGLE_MAPS_API_KEY?: string | undefined;
    SENTRY_DSN?: string | undefined;
    STRIPE_SECRET_KEY?: string | undefined;
    STRIPE_WEBHOOK_SECRET?: string | undefined;
    PAYPAL_CLIENT_ID?: string | undefined;
    PAYPAL_CLIENT_SECRET?: string | undefined;
};
export declare const isDevelopment: boolean;
export declare const isProduction: boolean;
export declare const isTest: boolean;
export declare const corsOrigins: string[];
export declare const uploadAllowedTypes: string[];
export declare const databaseConfig: {
    url: string;
};
export declare const redisConfig: {
    url: string;
};
export declare const jwtConfig: {
    secret: string;
    refreshSecret: string;
    expiresIn: string;
    refreshExpiresIn: string;
};
export declare const emailConfig: {
    host: string;
    port: number;
    user: string;
    pass: string;
    from: {
        email: string;
        name: string;
    };
};
export declare const uploadConfig: {
    maxSize: number;
    allowedTypes: string[];
    path: string;
    cdnUrl: string | undefined;
};
export declare const rateLimitConfig: {
    windowMs: number;
    maxRequests: number;
};
export declare const securityConfig: {
    bcryptRounds: number;
    sessionSecret: string;
};
export declare const externalApisConfig: {
    openai: {
        apiKey: string | undefined;
    };
    googleMaps: {
        apiKey: string | undefined;
    };
};
export declare const monitoringConfig: {
    logLevel: "error" | "warn" | "info" | "debug";
    sentryDsn: string | undefined;
};
export declare const paymentConfig: {
    stripe: {
        secretKey: string | undefined;
        webhookSecret: string | undefined;
    };
    paypal: {
        clientId: string | undefined;
        clientSecret: string | undefined;
    };
};
export default config;
//# sourceMappingURL=index.d.ts.map