import { Request, Response, NextFunction } from 'express';
export declare class AppError extends Error {
    statusCode: number;
    code: string;
    isOperational: boolean;
    details?: any;
    constructor(message: string, statusCode?: number, code?: string, isOperational?: boolean, details?: any);
}
/**
 * Global error handling middleware
 */
export declare const errorHandler: (error: Error, req: Request, res: Response, next: NextFunction) => void;
/**
 * Handle 404 errors (route not found)
 */
export declare const notFoundHandler: (req: Request, res: Response, next: NextFunction) => void;
/**
 * Async error wrapper - catches async errors and passes them to error handler
 */
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
/**
 * Create error helper functions
 */
export declare const createError: {
    badRequest: (message: string, code?: string, details?: any) => AppError;
    unauthorized: (message?: string, code?: string) => AppError;
    forbidden: (message?: string, code?: string) => AppError;
    notFound: (message?: string, code?: string) => AppError;
    conflict: (message: string, code?: string, details?: any) => AppError;
    unprocessableEntity: (message: string, code?: string, details?: any) => AppError;
    tooManyRequests: (message?: string, code?: string) => AppError;
    internal: (message?: string, code?: string) => AppError;
    serviceUnavailable: (message?: string, code?: string) => AppError;
};
export default errorHandler;
//# sourceMappingURL=error.d.ts.map