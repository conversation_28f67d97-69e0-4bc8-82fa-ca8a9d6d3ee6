"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logUserAction = exports.logApiResponse = exports.logDatabaseOperation = exports.logPerformance = exports.logSecurityEvent = exports.logError = exports.requestLogger = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const config_1 = require("../config");
// Custom log format
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.prettyPrint());
// Console format for development
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({
    format: 'HH:mm:ss',
}), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
        log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    return log;
}));
// Create logger instance
exports.logger = winston_1.default.createLogger({
    level: config_1.config.LOG_LEVEL,
    format: logFormat,
    defaultMeta: {
        service: 'freela-api',
        environment: config_1.config.NODE_ENV,
    },
    transports: [
        // Console transport
        new winston_1.default.transports.Console({
            format: config_1.isDevelopment ? consoleFormat : logFormat,
        }),
        // File transports
        new winston_1.default.transports.File({
            filename: 'logs/error.log',
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        new winston_1.default.transports.File({
            filename: 'logs/combined.log',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
    // Handle uncaught exceptions
    exceptionHandlers: [
        new winston_1.default.transports.File({
            filename: 'logs/exceptions.log',
        }),
    ],
    // Handle unhandled promise rejections
    rejectionHandlers: [
        new winston_1.default.transports.File({
            filename: 'logs/rejections.log',
        }),
    ],
});
// Create logs directory if it doesn't exist
const fs_1 = require("fs");
if (!(0, fs_1.existsSync)('logs')) {
    (0, fs_1.mkdirSync)('logs');
}
// Request logging middleware
const requestLogger = (req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        const { method, url, ip } = req;
        const { statusCode } = res;
        const logData = {
            method,
            url,
            statusCode,
            duration: `${duration}ms`,
            ip,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id,
        };
        if (statusCode >= 400) {
            exports.logger.warn('HTTP Request', logData);
        }
        else {
            exports.logger.info('HTTP Request', logData);
        }
    });
    next();
};
exports.requestLogger = requestLogger;
// Error logging helper
const logError = (error, context) => {
    exports.logger.error('Application Error', {
        message: error.message,
        stack: error.stack,
        context,
    });
};
exports.logError = logError;
// Security event logging
const logSecurityEvent = (event, details, req) => {
    exports.logger.warn('Security Event', {
        event,
        details,
        ip: req?.ip,
        userAgent: req?.get('User-Agent'),
        userId: req?.user?.id,
        timestamp: new Date().toISOString(),
    });
};
exports.logSecurityEvent = logSecurityEvent;
// Performance logging
const logPerformance = (operation, duration, metadata) => {
    exports.logger.info('Performance Metric', {
        operation,
        duration: `${duration}ms`,
        metadata,
    });
};
exports.logPerformance = logPerformance;
// Database operation logging
const logDatabaseOperation = (operation, table, duration, error) => {
    const logData = {
        operation,
        table,
        duration: `${duration}ms`,
    };
    if (error) {
        exports.logger.error('Database Operation Failed', {
            ...logData,
            error: error.message,
            stack: error.stack,
        });
    }
    else {
        exports.logger.debug('Database Operation', logData);
    }
};
exports.logDatabaseOperation = logDatabaseOperation;
// API response logging
const logApiResponse = (endpoint, statusCode, duration, userId) => {
    exports.logger.info('API Response', {
        endpoint,
        statusCode,
        duration: `${duration}ms`,
        userId,
    });
};
exports.logApiResponse = logApiResponse;
// User action logging
const logUserAction = (action, userId, details) => {
    exports.logger.info('User Action', {
        action,
        userId,
        details,
        timestamp: new Date().toISOString(),
    });
};
exports.logUserAction = logUserAction;
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map