{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../../../../src/middleware/security.ts"], "names": [], "mappings": ";;;;;;AACA,4EAA2C;AAC3C,oDAA4B;AAC5B,gDAAwB;AACxB,8DAAsC;AACtC,sCAAwE;AACxE,0CAAuC;AACvC,4CAA2D;AAG3D;;GAEG;AACU,QAAA,cAAc,GAAG,IAAA,cAAI,EAAC;IACjC,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;QAC3B,6DAA6D;QAC7D,IAAI,CAAC,MAAM;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEzC,qCAAqC;QACrC,IAAI,oBAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,oBAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9D,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,gDAAgD;QAChD,IAAI,sBAAa,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAClD,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,IAAA,yBAAgB,EAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAClE,QAAQ,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;IAC7C,CAAC;IACD,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;IAC7D,cAAc,EAAE;QACd,QAAQ;QACR,kBAAkB;QAClB,cAAc;QACd,QAAQ;QACR,eAAe;QACf,WAAW;QACX,kBAAkB;QAClB,cAAc;KACf;IACD,cAAc,EAAE;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;QAChB,YAAY;QACZ,wBAAwB;QACxB,oBAAoB;KACrB;IACD,MAAM,EAAE,KAAK,EAAE,WAAW;CAC3B,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,gBAAgB,GAAG,IAAA,gBAAM,EAAC;IACrC,qBAAqB,EAAE;QACrB,UAAU,EAAE;YACV,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;YACvC,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;YACrC,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACnB,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACpB,QAAQ,EAAE,CAAC,QAAQ,CAAC;SACrB;KACF;IACD,yBAAyB,EAAE,KAAK;IAChC,IAAI,EAAE;QACJ,MAAM,EAAE,QAAQ;QAChB,iBAAiB,EAAE,IAAI;QACvB,OAAO,EAAE,IAAI;KACd;CACF,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,qBAAqB,GAAG,IAAA,qBAAW,EAAC;IAC/C,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACnB,IAAI,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACpC,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,qBAAW,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IACD,KAAK,EAAE,CAAC;IACR,SAAS,EAAE,IAAI;CAChB,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,gBAAgB;IACZ,QAAQ,CAAS;IACjB,WAAW,CAAS;IAE5B,YAAY,QAAgB,EAAE,WAAmB;QAC/C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAW;QACzB,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAG,cAAc,GAAG,IAAI,MAAM,EAAE,CAAC;YAE/C,oBAAoB;YACpB,MAAM,OAAO,GAAG,MAAM,aAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE9C,IAAI,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC9B,MAAM,SAAS,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,CAAC;oBACZ,SAAS;iBACV,CAAC;YACJ,CAAC;YAED,oBAAoB;YACpB,MAAM,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC;YAC3B,MAAM,aAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;YAEhF,MAAM,SAAS,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ;gBACtC,SAAS;aACV,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YACzD,6CAA6C;YAC7C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,CAAC,WAAW;gBAC3B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAED;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAA,4BAAS,EAAC;IAC3C,QAAQ,EAAE,wBAAe,CAAC,QAAQ;IAClC,GAAG,EAAE,wBAAe,CAAC,WAAW;IAChC,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,2CAA2C;QACpD,IAAI,EAAE,qBAAqB;KAC5B;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACpB,IAAA,yBAAgB,EAAC,qBAAqB,EAAE;YACtC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,QAAQ,EAAE,GAAG,CAAC,IAAI;SACnB,EAAE,GAAG,CAAC,CAAC;QAER,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,2CAA2C;YACpD,IAAI,EAAE,qBAAqB;SAC5B,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,IAAA,4BAAS,EAAC;IAC/C,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;IACvC,GAAG,EAAE,CAAC,EAAE,wBAAwB;IAChC,sBAAsB,EAAE,IAAI;IAC5B,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,0DAA0D;QACnE,IAAI,EAAE,0BAA0B;KACjC;IACD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACpB,IAAA,yBAAgB,EAAC,0BAA0B,EAAE;YAC3C,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,QAAQ,EAAE,GAAG,CAAC,IAAI;YAClB,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK;SACvB,EAAE,GAAG,CAAC,CAAC;QAER,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,0DAA0D;YACnE,IAAI,EAAE,0BAA0B;SACjC,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC;AAEH;;GAEG;AACI,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAE,WAAmB,EAAE,EAAE;IACzE,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAE5D,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC/D,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,IAAI,SAAS,CAAC;YAC/D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAE3C,yBAAyB;YACzB,GAAG,CAAC,GAAG,CAAC;gBACN,mBAAmB,EAAE,WAAW,CAAC,QAAQ,EAAE;gBAC3C,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;gBACpD,mBAAmB,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;aAC9D,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,IAAA,yBAAgB,EAAC,wBAAwB,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;gBACxD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,gCAAgC;oBACzC,IAAI,EAAE,wBAAwB;iBAC/B,CAAC,CAAC;YACL,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC1D,IAAI,EAAE,CAAC,CAAC,oBAAoB;QAC9B,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AA9BW,QAAA,iBAAiB,qBA8B5B;AAEF;;GAEG;AACI,MAAM,mBAAmB,GAAG,CAAC,QAAgB,EAAE,WAAmB,EAAE,EAAE;IAC3E,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAE5D,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC/D,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACd,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAE/C,yBAAyB;YACzB,GAAG,CAAC,GAAG,CAAC;gBACN,mBAAmB,EAAE,WAAW,CAAC,QAAQ,EAAE;gBAC3C,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;gBACpD,mBAAmB,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;aAC9D,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,IAAA,yBAAgB,EAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;gBAC9D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,kCAAkC;oBAC3C,IAAI,EAAE,0BAA0B;iBACjC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC5D,IAAI,EAAE,CAAC,CAAC,oBAAoB;QAC9B,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAlCW,QAAA,mBAAmB,uBAkC9B;AAEF;;GAEG;AACI,MAAM,gBAAgB,GAAG,CAAC,UAAkB,MAAM,EAAE,EAAE;IAC3D,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,MAAM,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAEhD,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;YAExC,IAAI,IAAI,GAAG,YAAY,EAAE,CAAC;gBACxB,IAAA,yBAAgB,EAAC,uBAAuB,EAAE;oBACxC,IAAI;oBACJ,OAAO,EAAE,YAAY;oBACrB,QAAQ,EAAE,GAAG,CAAC,IAAI;iBACnB,EAAE,GAAG,CAAC,CAAC;gBAER,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,0BAA0B;oBACnC,IAAI,EAAE,mBAAmB;iBAC1B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAzBW,QAAA,gBAAgB,oBAyB3B;AAEF;;GAEG;AACH,MAAM,SAAS,GAAG,CAAC,IAAY,EAAU,EAAE;IACzC,MAAM,KAAK,GAA8B;QACvC,CAAC,EAAE,CAAC;QACJ,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,IAAI,GAAG,IAAI;QACf,EAAE,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI;KACvB,CAAC;IAEF,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;IAC5E,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACzC,CAAC;IAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;IAE7B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACjF,2BAA2B;IAC3B,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;IACjC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAE3B,uBAAuB;IACvB,GAAG,CAAC,GAAG,CAAC;QACN,wBAAwB,EAAE,SAAS;QACnC,iBAAiB,EAAE,MAAM;QACzB,kBAAkB,EAAE,eAAe;QACnC,iBAAiB,EAAE,iCAAiC;QACpD,oBAAoB,EAAE,0CAA0C;KACjE,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAfW,QAAA,eAAe,mBAe1B;AAEF;;GAEG;AACI,MAAM,SAAS,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC3E,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,iBAAiB,EAAE,CAAC;IACjE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC;IACxC,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACnC,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AALW,QAAA,SAAS,aAKpB;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAAG,GAAW,EAAE;IACrC,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AACxE,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,2BAA2B,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC7F,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IAC9C,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;IAElB,gCAAgC;IAChC,MAAM,kBAAkB,GAAG;QACzB,MAAM;QACN,UAAU;QACV,SAAS;QACT,UAAU;QACV,OAAO;QACP,UAAU;KACX,CAAC;IAEF,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAEjF,IAAI,YAAY,EAAE,CAAC;QACjB,IAAA,yBAAgB,EAAC,uBAAuB,EAAE;YACxC,SAAS;YACT,EAAE;YACF,QAAQ,EAAE,GAAG,CAAC,IAAI;SACnB,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAED,uDAAuD;IACvD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC9C,MAAM,oBAAoB,GAAG;QAC3B,mEAAmE;QACnE,2BAA2B;KAC5B,CAAC;IAEF,MAAM,eAAe,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAExF,IAAI,eAAe,EAAE,CAAC;QACpB,IAAA,yBAAgB,EAAC,uBAAuB,EAAE;YACxC,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,EAAE;YACF,QAAQ,EAAE,GAAG,CAAC,IAAI;SACnB,EAAE,GAAG,CAAC,CAAC;QAER,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,4BAA4B;YACrC,IAAI,EAAE,oBAAoB;SAC3B,CAAC,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAhDW,QAAA,2BAA2B,+BAgDtC;AAEF,kBAAe;IACb,IAAI,EAAE,sBAAc;IACpB,MAAM,EAAE,wBAAgB;IACxB,WAAW,EAAE,6BAAqB;IAClC,SAAS,EAAE,2BAAmB;IAC9B,aAAa,EAAE,+BAAuB;IACtC,gBAAgB,EAAhB,wBAAgB;IAChB,eAAe,EAAf,uBAAe;IACf,SAAS,EAAT,iBAAS;IACT,2BAA2B,EAA3B,mCAA2B;IAC3B,iBAAiB,EAAjB,yBAAiB;IACjB,mBAAmB,EAAnB,2BAAmB;CACpB,CAAC"}