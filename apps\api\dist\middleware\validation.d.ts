import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema } from 'zod';
/**
 * Generic validation middleware factory
 */
export declare const validate: (schema: ZodSchema, source?: "body" | "query" | "params") => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validate request body
 */
export declare const validateBody: (schema: ZodSchema) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validate query parameters
 */
export declare const validateQuery: (schema: ZodSchema) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validate URL parameters
 */
export declare const validateParams: (schema: ZodSchema) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validate multiple sources at once
 */
export declare const validateMultiple: (schemas: {
    body?: ZodSchema;
    query?: ZodSchema;
    params?: ZodSchema;
}) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare const commonSchemas: {
    pagination: z.ZodObject<{
        page: z.ZodDefault<z.ZodPipeline<z.ZodEffects<z.ZodString, number, string>, z.ZodNumber>>;
        limit: z.ZodDefault<z.ZodPipeline<z.ZodEffects<z.ZodString, number, string>, z.ZodNumber>>;
        sortBy: z.ZodOptional<z.ZodString>;
        sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
    }, "strip", z.ZodTypeAny, {
        limit: number;
        page: number;
        sortOrder: "asc" | "desc";
        sortBy?: string | undefined;
    }, {
        limit?: string | undefined;
        page?: string | undefined;
        sortBy?: string | undefined;
        sortOrder?: "asc" | "desc" | undefined;
    }>;
    idParam: z.ZodObject<{
        id: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        id: string;
    }, {
        id: string;
    }>;
    search: z.ZodObject<{
        q: z.ZodString;
        category: z.ZodOptional<z.ZodString>;
        tags: z.ZodOptional<z.ZodString>;
        minPrice: z.ZodOptional<z.ZodPipeline<z.ZodEffects<z.ZodString, number, string>, z.ZodNumber>>;
        maxPrice: z.ZodOptional<z.ZodPipeline<z.ZodEffects<z.ZodString, number, string>, z.ZodNumber>>;
        location: z.ZodOptional<z.ZodString>;
        sortBy: z.ZodDefault<z.ZodEnum<["relevance", "price", "rating", "date"]>>;
        sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
    }, "strip", z.ZodTypeAny, {
        sortBy: "relevance" | "price" | "rating" | "date";
        sortOrder: "asc" | "desc";
        q: string;
        category?: string | undefined;
        tags?: string | undefined;
        minPrice?: number | undefined;
        maxPrice?: number | undefined;
        location?: string | undefined;
    }, {
        q: string;
        sortBy?: "relevance" | "price" | "rating" | "date" | undefined;
        sortOrder?: "asc" | "desc" | undefined;
        category?: string | undefined;
        tags?: string | undefined;
        minPrice?: string | undefined;
        maxPrice?: string | undefined;
        location?: string | undefined;
    }>;
    fileUpload: z.ZodObject<{
        filename: z.ZodString;
        mimeType: z.ZodString;
        size: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        filename: string;
        mimeType: string;
        size: number;
    }, {
        filename: string;
        mimeType: string;
        size: number;
    }>;
    language: z.ZodObject<{
        lang: z.ZodDefault<z.ZodEnum<["ar", "en"]>>;
    }, "strip", z.ZodTypeAny, {
        lang: "ar" | "en";
    }, {
        lang?: "ar" | "en" | undefined;
    }>;
    location: z.ZodObject<{
        governorate: z.ZodString;
        city: z.ZodString;
        district: z.ZodOptional<z.ZodString>;
        coordinates: z.ZodOptional<z.ZodObject<{
            latitude: z.ZodNumber;
            longitude: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            latitude: number;
            longitude: number;
        }, {
            latitude: number;
            longitude: number;
        }>>;
    }, "strip", z.ZodTypeAny, {
        governorate: string;
        city: string;
        district?: string | undefined;
        coordinates?: {
            latitude: number;
            longitude: number;
        } | undefined;
    }, {
        governorate: string;
        city: string;
        district?: string | undefined;
        coordinates?: {
            latitude: number;
            longitude: number;
        } | undefined;
    }>;
    dateRange: z.ZodEffects<z.ZodObject<{
        startDate: z.ZodOptional<z.ZodString>;
        endDate: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }>, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }>;
    priceRange: z.ZodEffects<z.ZodObject<{
        minPrice: z.ZodOptional<z.ZodNumber>;
        maxPrice: z.ZodOptional<z.ZodNumber>;
        currency: z.ZodDefault<z.ZodEnum<["USD", "SYP"]>>;
    }, "strip", z.ZodTypeAny, {
        currency: "USD" | "SYP";
        minPrice?: number | undefined;
        maxPrice?: number | undefined;
    }, {
        minPrice?: number | undefined;
        maxPrice?: number | undefined;
        currency?: "USD" | "SYP" | undefined;
    }>, {
        currency: "USD" | "SYP";
        minPrice?: number | undefined;
        maxPrice?: number | undefined;
    }, {
        minPrice?: number | undefined;
        maxPrice?: number | undefined;
        currency?: "USD" | "SYP" | undefined;
    }>;
};
export declare const validationHelpers: {
    /**
     * Create a validation middleware for pagination
     */
    pagination: () => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
    /**
     * Create a validation middleware for ID parameter
     */
    idParam: () => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
    /**
     * Create a validation middleware for search
     */
    search: () => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
    /**
     * Create a validation middleware for language preference
     */
    language: () => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
    /**
     * Create a validation middleware for date range
     */
    dateRange: () => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
    /**
     * Create a validation middleware for price range
     */
    priceRange: () => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
    /**
     * Validate Arabic text content
     */
    arabicText: (text: string) => boolean;
    /**
     * Validate Syrian phone number
     */
    syrianPhone: (phone: string) => boolean;
    /**
     * Validate file type
     */
    fileType: (filename: string, allowedTypes: string[]) => boolean;
    /**
     * Sanitize HTML content
     */
    sanitizeHtml: (html: string) => string;
    /**
     * Validate and normalize email
     */
    normalizeEmail: (email: string) => string;
    /**
     * Validate and normalize phone number
     */
    normalizePhone: (phone: string) => string;
};
export default validate;
//# sourceMappingURL=validation.d.ts.map