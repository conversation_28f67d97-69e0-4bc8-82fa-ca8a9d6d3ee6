// Core Types
const userTypes = require('./user');
Object.assign(module.exports, userTypes);

// Common utility types
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

export interface ErrorResponse {
  success: false;
  message: string;
  errors?: string[];
  code?: string;
}

// Location types for Syrian context
export interface Location {
  governorate: string; // محافظة
  city: string; // مدينة
  district?: string; // منطقة
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

// Syrian governorates
export type SyrianGovernorate =
  | 'damascus' // دمشق
  | 'aleppo' // حلب
  | 'homs' // حمص
  | 'hama' // حماة
  | 'latakia' // اللاذقية
  | 'tartus' // طرطوس
  | 'idlib' // إدلب
  | 'daraa' // درعا
  | 'sweida' // السويداء
  | 'quneitra' // القنيطرة
  | 'raqqa' // الرقة
  | 'deir_ez_zor' // دير الزور
  | 'hasaka' // الحسكة
  | 'damascus_countryside'; // ريف دمشق

// Language and localization
export type SupportedLanguage = 'ar' | 'en';
export type SupportedLocale = 'ar-SY' | 'en-US';

export interface LocalizedString {
  ar: string;
  en?: string;
}

// File upload types
export interface FileUpload {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
}

// Search and filtering
export interface SearchFilters {
  query?: string;
  category?: string;
  location?: Partial<Location>;
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  deliveryTime?: number;
  language?: SupportedLanguage;
}

// Notification types
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: LocalizedString;
  message: LocalizedString;
  data?: Record<string, any>;
  read: boolean;
  createdAt: Date;
}

export type NotificationType =
  | 'booking_request'
  | 'booking_accepted'
  | 'booking_rejected'
  | 'booking_completed'
  | 'payment_received'
  | 'message_received'
  | 'profile_approved'
  | 'service_approved'
  | 'system_announcement';

// Export all types for CommonJS compatibility
module.exports = {
  ...userTypes,
};
