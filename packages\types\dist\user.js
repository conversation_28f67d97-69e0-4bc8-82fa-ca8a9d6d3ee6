"use strict";
// Note: These types are defined in index.ts to avoid circular dependencies
// BaseEntity, Location, LocalizedString, FileUpload
Object.defineProperty(exports, "__esModule", { value: true });
// Export all types for CommonJS compatibility
module.exports = {
    // User types
    User: {},
    UserRole: {},
    UserStatus: {},
    UserRegistration: {},
    UserLogin: {},
    UserProfile: {},
    ExpertProfile: {},
    ClientProfile: {},
    ExperienceLevel: {},
    CompanySize: {},
    Education: {},
    Certification: {},
    PortfolioItem: {},
    LanguageSkill: {},
    LanguageProficiency: {},
    Availability: {},
    WeekDay: {},
    ResponseTime: {},
    AuthTokens: {},
    PasswordReset: {},
    EmailVerification: {},
    PhoneVerification: {},
};
//# sourceMappingURL=user.js.map