import { Request, Response, NextFunction } from 'express';
import cors from 'cors';
/**
 * CORS configuration
 */
export declare const corsMiddleware: (req: cors.CorsRequest, res: {
    statusCode?: number | undefined;
    setHeader(key: string, value: string): any;
    end(): any;
}, next: (err?: any) => any) => void;
/**
 * Helmet security headers
 */
export declare const helmetMiddleware: (req: import("http").IncomingMessage, res: import("http").ServerResponse, next: (err?: unknown) => void) => void;
/**
 * Compression middleware
 */
export declare const compressionMiddleware: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
/**
 * General rate limiting middleware
 */
export declare const rateLimitMiddleware: import("express-rate-limit").RateLimitRequestHandler;
/**
 * Strict rate limiting for authentication endpoints
 */
export declare const authRateLimitMiddleware: import("express-rate-limit").RateLimitRequestHandler;
/**
 * IP-based rate limiting
 */
export declare const createIPRateLimit: (windowMs: number, maxRequests: number) => (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
/**
 * User-based rate limiting
 */
export declare const createUserRateLimit: (windowMs: number, maxRequests: number) => (req: Request, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
/**
 * Request size limiting middleware
 */
export declare const requestSizeLimit: (maxSize?: string) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Security headers middleware
 */
export declare const securityHeaders: (req: Request, res: Response, next: NextFunction) => void;
/**
 * Request ID middleware
 */
export declare const requestId: (req: Request, res: Response, next: NextFunction) => void;
/**
 * Suspicious activity detection
 */
export declare const suspiciousActivityDetection: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
declare const _default: {
    cors: (req: cors.CorsRequest, res: {
        statusCode?: number | undefined;
        setHeader(key: string, value: string): any;
        end(): any;
    }, next: (err?: any) => any) => void;
    helmet: (req: import("http").IncomingMessage, res: import("http").ServerResponse, next: (err?: unknown) => void) => void;
    compression: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
    rateLimit: import("express-rate-limit").RateLimitRequestHandler;
    authRateLimit: import("express-rate-limit").RateLimitRequestHandler;
    requestSizeLimit: (maxSize?: string) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
    securityHeaders: (req: Request, res: Response, next: NextFunction) => void;
    requestId: (req: Request, res: Response, next: NextFunction) => void;
    suspiciousActivityDetection: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
    createIPRateLimit: (windowMs: number, maxRequests: number) => (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
    createUserRateLimit: (windowMs: number, maxRequests: number) => (req: Request, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
};
export default _default;
//# sourceMappingURL=security.d.ts.map