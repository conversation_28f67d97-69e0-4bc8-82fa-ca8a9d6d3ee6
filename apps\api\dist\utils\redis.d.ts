import { RedisClientType } from 'redis';
declare class RedisClient {
    private client;
    private isConnected;
    constructor();
    private setupEventHandlers;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    isReady(): boolean;
    get(key: string): Promise<string | null>;
    set(key: string, value: string, ttl?: number): Promise<boolean>;
    del(key: string): Promise<boolean>;
    exists(key: string): Promise<boolean>;
    expire(key: string, ttl: number): Promise<boolean>;
    getJSON<T>(key: string): Promise<T | null>;
    setJSON<T>(key: string, value: T, ttl?: number): Promise<boolean>;
    hGet(key: string, field: string): Promise<string | null>;
    hSet(key: string, field: string, value: string): Promise<boolean>;
    hGetAll(key: string): Promise<Record<string, string> | null>;
    hDel(key: string, field: string): Promise<boolean>;
    lPush(key: string, value: string): Promise<boolean>;
    rPop(key: string): Promise<string | null>;
    sAdd(key: string, member: string): Promise<boolean>;
    sIsMember(key: string, member: string): Promise<boolean>;
    sRem(key: string, member: string): Promise<boolean>;
    flushAll(): Promise<boolean>;
    ping(): Promise<boolean>;
    getClient(): RedisClientType;
}
export declare const redis: RedisClient;
export declare const sessionHelpers: {
    setSession(sessionId: string, userId: string, data: any, ttl?: number): Promise<boolean>;
    getSession(sessionId: string): Promise<{
        userId: string;
        data: any;
        createdAt: string;
    } | null>;
    deleteSession(sessionId: string): Promise<boolean>;
    refreshSession(sessionId: string, ttl?: number): Promise<boolean>;
};
export declare const cacheHelpers: {
    cacheUserData(userId: string, data: any, ttl?: number): Promise<boolean>;
    getUserData(userId: string): Promise<any | null>;
    invalidateUserCache(userId: string): Promise<boolean>;
    cacheServiceData(serviceId: string, data: any, ttl?: number): Promise<boolean>;
    getServiceData(serviceId: string): Promise<any | null>;
    invalidateServiceCache(serviceId: string): Promise<boolean>;
};
export default redis;
//# sourceMappingURL=redis.d.ts.map