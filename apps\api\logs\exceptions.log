{
  error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
      at new PrismaClient (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\default.js:43:11)
      at <anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:9:46)
      at Object.<anonymous> (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:74:16)
      at Module._compile (node:internal/modules/cjs/loader:1554:14)
      at Object.transformer (C:\Users\<USER>\Documents\Freela\node_modules\tsx\dist\register-D2KMMyKp.cjs:2:1186)
      at Module.load (node:internal/modules/cjs/loader:1289:32)
      at Function._load (node:internal/modules/cjs/loader:1108:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
      at Module.require (node:internal/modules/cjs/loader:1311:12),
  level: 'error',
  message: 'uncaughtException: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:46)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:74:16)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)',
  stack: 'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at <anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:9:46)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts:74:16)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n' +
    '    at Object.transformer (C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs:2:1186)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1289:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1108:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:12)',
  exception: true,
  date: 'Tue Jun 10 2025 03:21:18 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 22932,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 103649280,
      heapTotal: 22339584,
      heapUsed: 15113352,
      external: 3256349,
      arrayBuffers: 174284
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 335657.296 },
  trace: [
    {
      column: 11,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\.prisma\\client\\default.js',
      function: 'new PrismaClient',
      line: 43,
      method: null,
      native: false
    },
    {
      column: 46,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts',
      function: '<anonymous>',
      line: 9,
      method: null,
      native: false
    },
    {
      column: 16,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\src\\client.ts',
      function: null,
      line: 74,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1554,
      method: '_compile',
      native: false
    },
    {
      column: 1186,
      file: 'C:\\Users\\<USER>\\Documents\\Freela\\node_modules\\tsx\\dist\\register-D2KMMyKp.cjs',
      function: 'Object.transformer',
      line: 2,
      method: 'transformer',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1289,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1108,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 220,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:21:18'
}
{
  error: Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\Users\<USER>\Documents\Freela\packages\database\dist\client' imported from C:\Users\<USER>\Documents\Freela\packages\database\dist\index.js
      at finalizeResolution (node:internal/modules/esm/resolve:275:11)
      at moduleResolve (node:internal/modules/esm/resolve:860:10)
      at defaultResolve (node:internal/modules/esm/resolve:984:11)
      at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)
      at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)
      at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)
      at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)
      at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)
      at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)
      at Module._compile (node:internal/modules/cjs/loader:1536:5) {
    code: 'ERR_MODULE_NOT_FOUND',
    url: 'file:///C:/Users/<USER>/Documents/Freela/packages/database/dist/client'
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  stack: "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  exception: true,
  date: 'Tue Jun 10 2025 03:30:45 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 36228,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\dist\\index.js'
    ],
    memoryUsage: {
      rss: 57012224,
      heapTotal: 18931712,
      heapUsed: 11238472,
      external: 2122639,
      arrayBuffers: 16659
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 336224.562 },
  trace: [
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'finalizeResolution',
      line: 275,
      method: null,
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'moduleResolve',
      line: 860,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'defaultResolve',
      line: 984,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.defaultResolve',
      line: 685,
      method: 'defaultResolve',
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/loader',
      function: '#cachedDefaultResolve',
      line: 634,
      method: null,
      native: false
    },
    {
      column: 53,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.getModuleJobForRequire',
      line: 384,
      method: 'getModuleJobForRequire',
      native: false
    },
    {
      column: 34,
      file: 'node:internal/modules/esm/module_job',
      function: 'new ModuleJobSync',
      line: 341,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.importSyncForRequire',
      line: 357,
      method: 'importSyncForRequire',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'loadESMFromCJS',
      line: 1385,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1536,
      method: '_compile',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:30:45'
}
{
  error: Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\Users\<USER>\Documents\Freela\packages\database\dist\client' imported from C:\Users\<USER>\Documents\Freela\packages\database\dist\index.js
      at finalizeResolution (node:internal/modules/esm/resolve:275:11)
      at moduleResolve (node:internal/modules/esm/resolve:860:10)
      at defaultResolve (node:internal/modules/esm/resolve:984:11)
      at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)
      at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)
      at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)
      at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)
      at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)
      at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)
      at Module._compile (node:internal/modules/cjs/loader:1536:5) {
    code: 'ERR_MODULE_NOT_FOUND',
    url: 'file:///C:/Users/<USER>/Documents/Freela/packages/database/dist/client'
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  stack: "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  exception: true,
  date: 'Tue Jun 10 2025 03:41:20 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 3904,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\dist\\index.js'
    ],
    memoryUsage: {
      rss: 56795136,
      heapTotal: 18669568,
      heapUsed: 11361624,
      external: 2122639,
      arrayBuffers: 16659
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 336858.875 },
  trace: [
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'finalizeResolution',
      line: 275,
      method: null,
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'moduleResolve',
      line: 860,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'defaultResolve',
      line: 984,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.defaultResolve',
      line: 685,
      method: 'defaultResolve',
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/loader',
      function: '#cachedDefaultResolve',
      line: 634,
      method: null,
      native: false
    },
    {
      column: 53,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.getModuleJobForRequire',
      line: 384,
      method: 'getModuleJobForRequire',
      native: false
    },
    {
      column: 34,
      file: 'node:internal/modules/esm/module_job',
      function: 'new ModuleJobSync',
      line: 341,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.importSyncForRequire',
      line: 357,
      method: 'importSyncForRequire',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'loadESMFromCJS',
      line: 1385,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1536,
      method: '_compile',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:41:20'
}
{
  error: Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\Users\<USER>\Documents\Freela\packages\database\dist\client' imported from C:\Users\<USER>\Documents\Freela\packages\database\dist\index.js
      at finalizeResolution (node:internal/modules/esm/resolve:275:11)
      at moduleResolve (node:internal/modules/esm/resolve:860:10)
      at defaultResolve (node:internal/modules/esm/resolve:984:11)
      at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)
      at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)
      at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)
      at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)
      at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)
      at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)
      at Module._compile (node:internal/modules/cjs/loader:1536:5) {
    code: 'ERR_MODULE_NOT_FOUND',
    url: 'file:///C:/Users/<USER>/Documents/Freela/packages/database/dist/client'
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  stack: "Error [ERR_MODULE_NOT_FOUND]: Cannot find module 'C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\client' imported from C:\\Users\\<USER>\\Documents\\Freela\\packages\\database\\dist\\index.js\n" +
    '    at finalizeResolution (node:internal/modules/esm/resolve:275:11)\n' +
    '    at moduleResolve (node:internal/modules/esm/resolve:860:10)\n' +
    '    at defaultResolve (node:internal/modules/esm/resolve:984:11)\n' +
    '    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:685:12)\n' +
    '    at #cachedDefaultResolve (node:internal/modules/esm/loader:634:25)\n' +
    '    at ModuleLoader.getModuleJobForRequire (node:internal/modules/esm/loader:384:53)\n' +
    '    at new ModuleJobSync (node:internal/modules/esm/module_job:341:34)\n' +
    '    at ModuleLoader.importSyncForRequire (node:internal/modules/esm/loader:357:11)\n' +
    '    at loadESMFromCJS (node:internal/modules/cjs/loader:1385:24)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1536:5)',
  exception: true,
  date: 'Tue Jun 10 2025 03:41:28 GMT+0200 (Mitteleuropäische Sommerzeit)',
  process: {
    pid: 38784,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.14.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\Freela\\apps\\api\\dist\\index.js'
    ],
    memoryUsage: {
      rss: 56893440,
      heapTotal: 18931712,
      heapUsed: 11265648,
      external: 2122639,
      arrayBuffers: 16659
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 336866.921 },
  trace: [
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'finalizeResolution',
      line: 275,
      method: null,
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'moduleResolve',
      line: 860,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/resolve',
      function: 'defaultResolve',
      line: 984,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.defaultResolve',
      line: 685,
      method: 'defaultResolve',
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/loader',
      function: '#cachedDefaultResolve',
      line: 634,
      method: null,
      native: false
    },
    {
      column: 53,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.getModuleJobForRequire',
      line: 384,
      method: 'getModuleJobForRequire',
      native: false
    },
    {
      column: 34,
      file: 'node:internal/modules/esm/module_job',
      function: 'new ModuleJobSync',
      line: 341,
      method: null,
      native: false
    },
    {
      column: 11,
      file: 'node:internal/modules/esm/loader',
      function: 'ModuleLoader.importSyncForRequire',
      line: 357,
      method: 'importSyncForRequire',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'loadESMFromCJS',
      line: 1385,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1536,
      method: '_compile',
      native: false
    }
  ],
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 03:41:28'
}
