export * from './validation';
export declare const generateRandomString: (length?: number) => string;
export declare const generateNumericCode: (length?: number) => string;
export declare const isValidEmail: (email: string) => boolean;
export declare const normalizeEmail: (email: string) => string;
export declare const isStrongPassword: (password: string) => boolean;
export declare const sanitizeInput: (input: string) => string;
//# sourceMappingURL=auth.d.ts.map