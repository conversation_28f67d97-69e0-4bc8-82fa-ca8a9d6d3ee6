{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "noEmit": false, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "types": ["node"]}, "include": ["src/**/*", "locales/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"]}